FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy pyproject.toml first to leverage Docker cache
COPY pyproject.toml /app/

# Install Python dependencies from pyproject.toml
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -e .

# Copy project files
COPY . /app/

# Make entrypoint script executable
RUN chmod +x /app/.dockerwrapper/docker-entrypoint.sh

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=flask_app.py

# Expose port for Flask
EXPOSE 5000

# Set entrypoint
ENTRYPOINT ["/app/.dockerwrapper/docker-entrypoint.sh"]

# Command to run Flask
CMD ["flask", "run", "--host", "0.0.0.0", "--port", "5000"] 