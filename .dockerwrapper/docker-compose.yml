version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: regrigor-postgres
    environment:
      POSTGRES_USER: regrigor
      POSTGRES_PASSWORD: regrigor_password
      POSTGRES_DB: regrigor_db
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5440:5432"
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "regrigor"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: regrigor-redis
    ports:
      - "9010:6379"
    volumes:
      - redisdata:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  api:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
    container_name: regrigor-api
    environment:
      - DATABASE_URL=*****************************************************/regrigor_db
      - SESSION_SECRET=dev_session_secret_key_12345
      - JWT_SECRET_KEY=dev_jwt_secret_key_67890
      - JWT_ALGORITHM=HS256
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "8010:5001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ..:/app
      - /app/venv
    command: uvicorn api.main:app --reload --host 0.0.0.0 --port 5001
    restart: unless-stopped

  web:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.flask
    container_name: regrigor-web
    environment:
      - DATABASE_URL=*****************************************************/regrigor_db
      - SESSION_SECRET=dev_session_secret_key_12345
      - JWT_SECRET_KEY=dev_jwt_secret_key_67890
      - JWT_ALGORITHM=HS256
      - FLASK_APP=flask_app.py
      - FLASK_DEBUG=1
      - REDIS_URL=redis://redis:6379/0
      - API_URL=http://api:5001
    ports:
      - "3010:5000"
    depends_on:
      postgres:
        condition: service_healthy
      api:
        condition: service_started
      redis:
        condition: service_healthy
    volumes:
      - ..:/app
      - /app/venv
    command: flask run --host 0.0.0.0 --port 5000
    restart: unless-stopped

volumes:
  pgdata:
  redisdata: 