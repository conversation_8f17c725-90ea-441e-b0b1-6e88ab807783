#!/bin/bash
set -e

# Print environment info
echo "Starting RegrigorRigor application..."
echo "Python version: $(python --version)"
echo "Working directory: $(pwd)"

# Wait for the PostgreSQL database and initialize
python /app/.dockerwrapper/init_db.py

# Run migrations if we have alembic
if [ -f /app/alembic.ini ]; then
  echo "Running database migrations..."
  # Use alembic stamp to mark the current migration as applied without running it
  # This helps when tables already exist but alembic doesn't know about them
  alembic stamp head || echo "Alembic stamp failed, continuing anyway..."
  
  # Now try to run migrations, but don't fail if they already exist
  alembic upgrade head || echo "Alembic upgrade failed, continuing anyway..."
fi

# Execute the command passed to docker-entrypoint
exec "$@" 