<p align="center">
  <img src="https://via.placeholder.com/200x80/2196F3/FFFFFF?text=Regression+Rigor" alt="Regression Rigor Logo" width="200"/>
</p>

<h1 align="center">Regression Rigor</h1>

<p align="center">
  <strong>Advanced Cybersecurity Testing and Validation Platform</strong>
</p>

<p align="center">
  <img src="https://img.shields.io/badge/build-passing-brightgreen" alt="Build Status">
  <img src="https://img.shields.io/badge/coverage-85%25-green" alt="Coverage">
  <a href="https://github.com/forkrul/Replit-RegressionRigor/blob/main/LICENSE">
    <img src="https://img.shields.io/badge/license-AGPL--3.0-blue" alt="License">
  </a>
  <img src="https://img.shields.io/badge/version-v0.2.0-blue" alt="Version">
  <a href="https://github.com/psf/black">
    <img src="https://img.shields.io/badge/code%20style-black-000000.svg" alt="Code style: black">
  </a>
  <img src="https://img.shields.io/badge/python-3.10%2B-blue" alt="Python Version">
</p>

<p align="center">
  <a href="#key-features">Key Features</a> •
  <a href="#installation">Installation</a> •
  <a href="#quick-start">Quick Start</a> •
  <a href="#documentation">Documentation</a> •
  <a href="#architecture">Architecture</a> •
  <a href="#contributing">Contributing</a> •
  <a href="#security">Security</a> •
  <a href="#license">License</a>
</p>

---

## Overview

Regression Rigor is a comprehensive cybersecurity testing and validation platform designed to help organizations systematically test, validate, and document their security controls against the MITRE ATT&CK® framework. The platform enables security teams to create, manage, and execute test cases that simulate real-world attack techniques, providing measurable evidence of security control effectiveness.

## Key Features

### 🔒 **Enhanced Authentication & Security**
- **Multi-Factor Authentication (2FA)**: TOTP-based 2FA with backup codes for enhanced security
- **Advanced Password Policies**: Configurable password complexity requirements and account lockout protection
- **Role-Based Access Control**: Granular permissions system (Admin, Analyst, Viewer) with audit logging
- **Account Management**: Self-service password reset, account unlock, and profile management

### 🔗 **Testcase Chaining & Sequencing**
- **Visual Chain Designer**: Drag-and-drop interface for creating complex attack chains
- **Conditional Logic**: Support for decision nodes and conditional execution paths
- **Real-time Execution Monitoring**: Live status updates and progress tracking for chain executions
- **Dependency Management**: Automatic ordering and prerequisite validation

### 🎯 **MITRE ATT&CK Integration**
- **Full Framework Support**: Complete integration with MITRE ATT&CK framework (v17+)
- **Technique Mapping**: Map test cases to specific techniques, tactics, and procedures
- **Coverage Analysis**: Visualize security control coverage across the attack matrix
- **Navigator Integration**: Interactive MITRE Navigator for technique visualization

### 📊 **Advanced Test Management**
- **Campaign Management**: Organize test cases into structured campaigns with scheduling
- **Comprehensive Audit Trails**: Full activity logging and change tracking
- **Evidence Management**: Attach and manage evidence files for test executions
- **Rich Reporting**: Detailed reports with metrics, trends, and compliance mapping

### 🚀 **Modern Architecture**
- **API-First Design**: Comprehensive REST API for integration with existing security tools
- **React Frontend**: Modern, responsive UI with real-time updates
- **Scalable Backend**: FastAPI-based services with PostgreSQL and Redis
- **Extensible Framework**: Plugin system for custom integrations and modules

## Installation

### Prerequisites

- Python 3.10+
- PostgreSQL 14+
- Redis 6+
- Node.js 18+ (for frontend development)

### Using Docker (Recommended)

```bash
# Clone the repository
git clone https://github.com/forkrul/Replit-RegressionRigor.git
cd Replit-RegressionRigor

# Start the services using Docker Compose
docker-compose up -d
```

### Manual Installation

```bash
# Clone the repository
git clone https://github.com/forkrul/Replit-RegressionRigor.git
cd Replit-RegressionRigor

# Create and activate a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up the database
python -m api.database init

# Start the API server
uvicorn api.main:app --reload --host 0.0.0.0 --port 8010

# In a separate terminal, start the web server
python flask_app.py
```

## Quick Start

After installation, you can access:

- **Web Interface**: http://localhost:3010
- **API Documentation**: http://localhost:8010/docs
- **API Redoc**: http://localhost:8010/redoc

### Initial Setup

1. Create an admin user:
   ```bash
   python -m api.cli create-user --username admin --email <EMAIL> --password secure_password --role admin
   ```

2. Import the latest MITRE ATT&CK data:
   ```bash
   python -m api.cli import-mitre --domain enterprise
   ```

3. Log in to the web interface and start creating test cases and campaigns.

### Example: Creating a Test Case via API

```python
import requests
import json

# Authenticate and get token
auth_response = requests.post(
    "http://localhost:8010/api/v1/auth/token/",
    data={"username": "admin", "password": "secure_password"}
)
token = auth_response.json()["access_token"]
headers = {"Authorization": f"Bearer {token}"}

# Create a test case
test_case = {
    "name": "Test Windows Command Execution",
    "description": "Verify detection of suspicious command execution",
    "expected_result": "Alert generated by EDR solution",
    "mitre_technique_ids": ["T1059.003"],
    "steps": [
        {
            "name": "Execute PowerShell command",
            "description": "Run encoded PowerShell command",
            "command": "powershell -e UwB0AGEAcgB0AC0AUAByAG8AYwBlAHMAcwAgAGMAYQBsAGMALgBlAHgAZQA="
        }
    ]
}

response = requests.post(
    "http://localhost:8010/api/v1/test-cases/",
    headers=headers,
    json=test_case
)
print(json.dumps(response.json(), indent=2))
```

## Documentation

Comprehensive documentation is available in the `docs/` directory and online:

- [User Guide](docs/user-guide.md)
- [API Reference](docs/api-reference.md)
- [Administrator Guide](docs/admin-guide.md)
- [Developer Guide](docs/developer-guide.md)
- [Test Case Development Guide](docs/test-case-development.md)

## Architecture

Regression Rigor follows a modern, modular architecture:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Web Interface  │────▶│  API Services   │────▶│    Database     │
│  (Flask)        │     │  (FastAPI)      │     │  (PostgreSQL)   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │  ▲
                               │  │
                               ▼  │
                        ┌─────────────────┐
                        │                 │
                        │  Execution      │
                        │  Framework      │
                        │                 │
                        └─────────────────┘
                               │  ▲
                               │  │
                               ▼  │
                        ┌─────────────────┐
                        │                 │
                        │  Target         │
                        │  Environments   │
                        │                 │
                        └─────────────────┘
```

### Key Components

- **API Layer**: FastAPI-based REST API providing all core functionality
- **Web Interface**: Flask-based web application for user interaction
- **Execution Framework**: Manages test case execution across environments
- **Database**: PostgreSQL for persistent storage with SQLAlchemy ORM
- **Cache**: Redis for caching and task queue management
- **Authentication**: JWT-based authentication with role-based access control

## Contributing

We welcome contributions from the community! Please see our [Contributing Guide](CONTRIBUTING.md) for details on how to get started.

### Development Setup

```bash
# Clone the repository
git clone https://github.com/forkrul/Replit-RegressionRigor.git
cd Replit-RegressionRigor

# Create and activate a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -r requirements-dev.txt

# Set up pre-commit hooks
pre-commit install

# Run tests
pytest
```

## Security

We take security seriously. If you discover a security vulnerability, please send an <NAME_EMAIL> instead of opening a public issue.

See our [Security Policy](SECURITY.md) for more details.

## License

This project is licensed under the GNU Affero General Public License v3.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgements

- [MITRE ATT&CK®](https://attack.mitre.org/) for their comprehensive knowledge base of adversary tactics and techniques
- [FastAPI](https://fastapi.tiangolo.com/) for the high-performance API framework
- [SQLAlchemy](https://www.sqlalchemy.org/) for the SQL toolkit and ORM
- [Flask](https://flask.palletsprojects.com/) for the web framework
- [All Contributors](CONTRIBUTORS.md) who have helped shape this project

---

<p align="center">
  Made with ❤️ by the Regression Rigor Team
</p>
