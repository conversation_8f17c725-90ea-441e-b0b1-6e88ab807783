# RegressionRigor API

A robust FastAPI-based API application designed for comprehensive API management with advanced database integration and scalable architecture.

## Features

- FastAPI framework with PostgreSQL database
- SQLAlchemy ORM for data persistence 
- Pydantic for type safety and validation
- Babel internationalization support
- Modular routing with database-connected endpoints
- Comprehensive test suite with pytest
- MITRE ATT&CK Framework integration
- Database reporting and analytics
- Comprehensive mapping of MITRE ATT&CK techniques, tactics, and procedures
- Support for multiple ATT&CK versions and domains (Enterprise, Mobile, ICS)
- Technique relationship visualization
- Technique scoring system for risk assessment and prioritization
- Integration with threat intelligence feeds

## Enhanced Authentication System (v0.1.1)

The system now includes a robust authentication system with the following features:

- **User Registration and Email Verification**
  - Secure user registration with email verification
  - Resend verification email functionality
  - Prevention of email enumeration attacks

- **Two-Factor Authentication (2FA)**
  - Time-based One-Time Password (TOTP) support
  - QR code generation for easy setup with authenticator apps
  - Backup codes for account recovery
  - Complete 2FA management (setup, enable, disable, regenerate backup codes)

- **Account Security**
  - Strong password requirements and validation
  - Account locking after multiple failed login attempts
  - Automatic account unlocking after a time period
  - Secure password reset with expiring tokens
  - Password change with current password verification

- **Role-Based Access Control**
  - Granular permissions with admin, operator, and viewer roles
  - Role-based endpoint protection
  - Convenience dependencies for common role checks

- **Session Management**
  - User session tracking and management
  - Token invalidation when passwords are changed
  - User preferences for personalization

## Documentation

All project documentation is available in the [docs](docs/) directory:

- [Development Plan](docs/development_plan.md) - Comprehensive development plan
- [Docker Setup](docs/docker_setup.md) - Docker environment setup instructions
- [Architecture](docs/architecture.md) - System architecture and design
- [Schema](docs/schema.md) - Database schema documentation
- [Roadmap](docs/roadmap.md) - Future development roadmap

For a complete list of documentation, see the [Documentation Index](docs/index.md).

## MOSCOW Prioritization

### Must have
- **Project Structure:**
  - Hierarchical organization: DB → ASSESSMENT → CAMPAIGNS → TESTS
  - Complete CRUD operations with soft-delete functionality

- **Team Views:**
  - **Red Team:**
    - Name and description
    - Technique and phase
    - Operator guidance
    - Attack start/stop times
    - Targets
    - References
    - Attacker tools
  - **Blue Team:**
    - Outcome and outcome notes
    - Detection time
    - Defenses

- **Core Features:**
  - **MITRE TTP Database:** Comprehensive technique, tactic, and procedure database
  - **MITRE Attack Navigator:** Graphical overview of tests using MITRE ATT&CK Navigator
  - **API Design:** RESTful API implementation (not GraphQL)
  - **Tagging System:** Support for test cases, assessments, and campaigns
  - **Database Operations:** Cross-database copy functionality (beyond VECTR GUI capabilities)
  - **Search Functionality:** Advanced search with tag support
  - **Threat Complexity Management:** Test execution ordering (e.g., ransomware scenarios)

### Should have
- **Data Management:**
  - Export/Import capabilities for:
    - Database
    - Assessments
    - Campaigns
  - Test templates integration (e.g., Atomic Red Team index import in VECTR)

### Could have
- **Enhanced Team Views:**
  - **Red Team Extensions:**
    - Sources documentation
    - Execution artifacts tracking
  - **Blue Team Extensions:**
    - Detection mechanisms
    - Prevention strategies
    - Evidence files
    - Location information

- **Visualization Features:**
  - Graph creation tools
  - Campaign timeline visualization
  - Attack tree per campaign
  - Source knowledge base integration

## Must Follow Standards

### PEP Standards
All code must strictly adhere to the following Python Enhancement Proposals:

1. **PEP 8 - Style Guide**
   - Use 4 spaces for indentation
   - Line length limit: 79 characters for code, 72 for docstrings/comments
   - Import styling:
     ```python
     # Correct
     from module import (func1,
                        func2,
                        func3)
     ```
   - Two blank lines before top-level classes/functions
   - One blank line before class methods
   - Use snake_case for functions and variables
   - Use PascalCase for classes

2. **PEP 257 - Docstring Conventions**
   - All modules, functions, classes, and methods must have docstrings
   - Use triple double-quotes (`"""`) for docstrings
   - First line should be a summary line
   - Multi-line docstrings:
     ```python
     def complex_function(arg1: str, arg2: int) -> bool:
         """Calculate something important.

         Args:
             arg1: First argument description
             arg2: Second argument description

         Returns:
             Boolean indicating success
         """
     ```

3. **PEP 484 - Type Hints**
   - All function arguments and return values must have type hints
   - Use built-in typing module:
     ```python
     from typing import List, Optional, Dict

     def process_items(items: List[str]) -> Dict[str, int]:
         """Process string items into a dictionary."""
     ```
   - Use Optional[] for nullable values
   - Use Union[] for multiple possible types
   - Type hints are mandatory for public APIs

### Additional Coding Requirements
- Write clear, descriptive variable and function names
- Include comprehensive unit tests for all new code
- Handle edge cases and add appropriate error messages
- Document all dependencies in requirements files
- Keep functions focused and single-purpose
- Use constants for magic numbers and repeated strings
- Add logging for important operations

## MITRE ATT&CK Integration

### Overview
The application integrates with the MITRE ATT&CK Framework through a Git submodule. This allows for versioned tracking of MITRE ATT&CK data and provides a foundation for threat analysis and tracking.

### Data Structure
- **MitreVersion**: Tracks different versions of imported MITRE data
- **MitreTechnique**: Stores MITRE ATT&CK techniques
- **MitreTactic**: Stores MITRE ATT&CK tactics
- **Relationships**: Maintains technique-tactic relationships

### API Endpoints

#### Import MITRE Data
```http
POST /api/v1/mitre/import
```
Import MITRE ATT&CK data from the enterprise-attack.json file.

Query Parameters:
- `version` (optional): Version string override
- `set_as_current` (optional): Set as current version (default: true)

#### List Versions
```http
GET /api/v1/mitre/versions
```
List all imported MITRE versions.

#### List Techniques
```http
GET /api/v1/mitre/techniques
```
List MITRE techniques with optional filtering.

Query Parameters:
- `version_id` (optional): Specific version ID
- `tactic_id` (optional): Filter by tactic ID
- `page`: Page number (default: 1)
- `size`: Page size (default: 10)

#### List Tactics
```http
GET /api/v1/mitre/tactics
```
List MITRE tactics for a specific version.

Query Parameters:
- `version_id` (optional): Specific version ID

#### Technique Scores
```http
POST /api/v1/mitre/techniques/{technique_id}/scores
GET /api/v1/mitre/techniques/{technique_id}/scores
GET /api/v1/mitre/techniques/{technique_id}/with-scores
POST /api/v1/mitre/techniques/scores/bulk
GET /api/v1/mitre/techniques/scores/categories
GET /api/v1/mitre/techniques/top-scored
```

### Usage Example
1. Import MITRE data:
```bash
curl -X POST "http://localhost:5000/api/v1/mitre/import"
```

2. List techniques:
```bash
curl "http://localhost:5000/api/v1/mitre/techniques?page=1&size=10"
```

### Versioning
The system maintains multiple versions of MITRE data:
- Each import creates a new version
- One version can be marked as "current"
- Historical versions are preserved
- Relationships between techniques and tactics are version-specific

### Data Updates
To update MITRE data:
1. Update the Git submodule: `git submodule update --remote`
2. Import the new data using the API endpoint
3. Optionally set it as the current version

### Running Tests
To run the MITRE integration tests:
```bash
# Run all tests
python -m pytest tests/test_api/test_mitre.py -v

# Run with coverage report
python -m pytest tests/test_api/test_mitre.py -v --cov=api.models.mitre --cov=api.routes.v1_mitre --cov-report=term-missing
```

The test suite covers:
1. Data import functionality
2. Version management
3. Technique and tactic relationships
4. API endpoints behavior
5. Error handling and validation
6. Pagination

## Database Reporting

### Overview
The application provides comprehensive database reporting capabilities through a dedicated endpoint. This feature allows you to:
- List all database tables
- View row counts for each table
- Access sample data (up to 5 rows per table)
- Get internationalized error messages and status updates

### Database Report Endpoint
```http
GET /api/v1/db_report
```
Returns a detailed report of all tables in the public schema.

#### Example Request
```bash
curl "http://localhost:5000/api/v1/db_report"
```

#### Example Response
```json
{
  "users": {
    "total_rows": 10,
    "sample_data": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "created_time": "2025-02-25T20:23:34.847992"
      }
    ]
  },
  "campaigns": {
    "total_rows": 2,
    "sample_data": [
      {
        "id": 1,
        "name": "Example Campaign",
        "description": "Test campaign",
        "status": "active",
        "created_time": "2025-02-25T20:23:34.847992",
        "updated_time": "2025-02-25T20:23:34.847992"
      }
    ]
  }
}
```

#### Error Responses
- `404 Not Found`: No tables found in database
- `500 Internal Server Error`: Error accessing database tables

Error messages are internationalized and will be returned in the user's preferred language.

#### Internationalization
The endpoint supports multiple languages through the `Accept-Language` header:
```bash
# Request in German
curl -H "Accept-Language: de" "http://localhost:5000/api/v1/db_report"

# Request in Spanish
curl -H "Accept-Language: es" "http://localhost:5000/api/v1/db_report"
```

## Threat-Defense Mapping

### Endpoints Overview

#### Get Attack Path Coverage Visualization
```http
POST /api/v1/threat-defense/coverage/visualization
```
Generate a mermaid visualization for attack path coverage analysis.

Request Body:
```json
{
  "technique_sequence": ["T1566", "T1078"]
}
```

Response:
```json
{
  "mermaid_chart": "string",
  "overall_coverage": 0.75,
  "average_coverage": 0.65
}
```

Parameters:
- `technique_sequence`: Array of MITRE ATT&CK technique IDs representing an attack path

Response Fields:
- `mermaid_chart`: Mermaid.js compatible flowchart showing techniques and countermeasures
- `overall_coverage`: Coverage score considering sequential dependencies (0.0 to 1.0)
- `average_coverage`: Average coverage across all techniques (0.0 to 1.0)


#### D3FEND Integration

##### List D3FEND Controls
```http
GET /api/v1/d3fend/controls
```
List available D3FEND defensive controls.

Query Parameters:
- `page`: Page number (default: 1)
- `size`: Results per page (default: 10, max: 100)

##### Get D3FEND Control Details
```http
GET /api/v1/d3fend/controls/{control_id}
```
Get detailed information about a specific D3FEND control.


## Getting Started

### Prerequisites

- Python 3.11+
- PostgreSQL database
- Required Python packages (see `pyproject.toml`)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Set up environment variables:
   - `DATABASE_URL`: PostgreSQL connection string
   - Other configuration variables as needed

### Running the Application

1. Start the FastAPI server:
   ```bash
   uvicorn api.main:app --host 0.0.0.0 --port 5000 --reload
   ```

2. Start the Streamlit UI:
   ```bash
   streamlit run app.py --server.port 8501 --server.address 0.0.0.0
   ```

3. Access:
   - API documentation: http://localhost:5000/docs
   - OpenAPI specification: http://localhost:5000/openapi.json
   - Streamlit UI: http://localhost:8501


## API Documentation

API documentation is available in two formats:
1. Swagger UI at `/docs` when the server is running
2. OpenAPI JSON specification at `/openapi.json`


## Running Tests

```bash
bash scripts/run_tests.sh
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Feature Management System

This project includes a comprehensive feature management system to track development progress and generate reports. The system helps manage the development lifecycle of features across different phases (API, Tests, UI).

### Using the Management Script

The `manage` script provides a unified interface to all feature management tools:

```bash
# Show help
./manage help

# Manage feature development
./manage feature --start-api dashboard
./manage feature --complete-api dashboard

# Create a new feature with all components
./manage new --create user-management

# Show feature development status
./manage status

# Generate the feature development dashboard
./manage dashboard

# Generate a project report
./manage report

# Update the development roadmap
./manage update
```

### Feature Development Workflow

1. Start API development with `./manage feature --start-api FEATURE_NAME`
2. Complete API development with `./manage feature --complete-api FEATURE_NAME`
3. Start test development with `./manage feature --start-test FEATURE_NAME`
4. Complete test development with `./manage feature --complete-test FEATURE_NAME`
5. Start UI development with `./manage feature --start-ui FEATURE_NAME`
6. Complete UI development with `./manage feature --complete-ui FEATURE_NAME`

### Visualization and Reporting

- **Dashboard**: An HTML dashboard is generated at `docs/dashboard.html` that provides a visual overview of feature development status.
- **Project Reports**: Project reports are generated at `docs/project_report_DATE.md` with statistics and recommendations.

For more detailed information about the feature management system, see the [Feature Management Documentation](.dockerwrapper/README.md).