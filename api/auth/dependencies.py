"""Authentication dependencies for FastAPI."""
from fastapi import Depends, HTTPException, status, Security
from fastapi.security import OA<PERSON>2<PERSON><PERSON><PERSON>Bearer
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import logging
from typing import List

from api.database import get_db
from api.models.user import User, UserRole

# JWT configuration
SECRET_KEY = "your-secret-key"  # In production, use a secure environment variable
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/token")

logger = logging.getLogger(__name__)

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user from JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception

    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is inactive"
        )
    
    # Check if account is locked
    if user.account_locked:
        # Check if lock has expired (locks expire after 24 hours)
        if user.account_locked_at and user.account_locked_at < datetime.utcnow() - timedelta(hours=24):
            # Unlock account
            user.account_locked = False
            user.account_locked_at = None
            user.failed_login_attempts = 0
            db.commit()
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account is locked due to too many failed login attempts"
            )
    
    # Check if password has been changed since the token was issued
    if "iat" in payload and user.password_changed_at:
        token_issued_at = datetime.fromtimestamp(payload["iat"])
        if user.password_changed_at > token_issued_at:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Password has been changed since token was issued",
                headers={"WWW-Authenticate": "Bearer"},
            )

    return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """Get current active user."""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user",
        )
    return current_user

async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """Get current admin user."""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions",
        )
    return current_user

async def admin_required(
    current_user: User = Depends(get_current_admin_user),
) -> User:
    """
    Dependency that requires admin privileges.
    
    This is an alias for get_current_admin_user to provide a more
    descriptive name for the dependency.
    """
    return current_user

def has_role(required_roles: List[UserRole]):
    """Dependency for role-based access control.
    
    Args:
        required_roles: List of roles that are allowed to access the endpoint
        
    Returns:
        A dependency function that checks if the user has one of the required roles
    """
    def role_checker(current_user: User = Depends(get_current_active_user)) -> User:
        """Check if the user has one of the required roles.
        
        Args:
            current_user: The authenticated active user
            
        Returns:
            The authenticated active user if they have one of the required roles
            
        Raises:
            HTTPException: If the user doesn't have one of the required roles
        """
        if current_user.role not in required_roles:
            logger.warning(
                f"User {current_user.username} with role {current_user.role} "
                f"attempted to access a resource requiring one of {required_roles}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    
    return role_checker

# Convenience dependencies for common role checks
def admin_only(current_user: User = Security(has_role([UserRole.ADMIN]))) -> User:
    """Dependency for admin-only endpoints."""
    return current_user

def operator_or_admin(
    current_user: User = Security(has_role([UserRole.ADMIN, UserRole.OPERATOR]))
) -> User:
    """Dependency for endpoints that require operator or admin role."""
    return current_user

def has_permission(permission: str):
    """Dependency for permission-based access control.
    
    Args:
        permission: The required permission
        
    Returns:
        A dependency function that checks if the user has the required permission
    """
    def permission_checker(current_user: User = Depends(get_current_active_user)) -> User:
        """Check if the user has the required permission.
        
        Args:
            current_user: The authenticated active user
            
        Returns:
            The authenticated active user if they have the required permission
            
        Raises:
            HTTPException: If the user doesn't have the required permission
        """
        # Admin users have all permissions
        if current_user.role == UserRole.ADMIN:
            return current_user
        
        # Check specific permissions
        if permission == "create_test_case":
            if current_user.role in [UserRole.ADMIN, UserRole.OPERATOR, UserRole.ANALYST]:
                return current_user
        elif permission == "admin":
            if current_user.role == UserRole.ADMIN:
                return current_user
        
        logger.warning(
            f"User {current_user.username} with role {current_user.role} "
            f"attempted to access a resource requiring permission {permission}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    
    return permission_checker
