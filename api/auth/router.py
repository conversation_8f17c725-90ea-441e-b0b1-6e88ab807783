"""Authentication router for the FastAPI application."""

import logging
import secrets
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, Optional

from fastapi import (
    <PERSON>Router,
    BackgroundTasks,
    Body,
    Depends,
    HTTPException,
    Security,
    status,
)
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRe<PERSON>Form
from jose import JWTError, jwt
from pydantic import EmailStr
from sqlalchemy.orm import Session

from api.auth.utils import (
    create_user,
    send_password_reset_email,
    send_verification_email,
    send_account_unlock_email,
    validate_email,
    validate_password,
)
from api.database import get_db
from api.dependencies import AL<PERSON><PERSON><PERSON>H<PERSON>, SECRET_KEY, get_current_user
from api.models.schemas import (
    EmailVerificationRequest,
    LoginResponse,
    PasswordReset,
    PasswordResetRequest,
    TwoFactorVerify,
    UserCreate,
    UserResponse,
)
from api.models.user import User, UserRole
from api.auth.dependencies import SECRET_KEY, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get_current_user
from api.auth.utils import create_user, validate_password, send_password_reset_email, send_verification_email, validate_email
from api.models.schemas import TwoFactorVerify, LoginResponse, UserCreate, UserResponse, PasswordResetRequest, PasswordReset, EmailVerificationRequest
from api.utils.rate_limiter import auth_rate_limit

logger = logging.getLogger(__name__)

# Authentication configuration
ACCESS_TOKEN_EXPIRE_MINUTES = 30
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token", auto_error=False)

# Create the router
router = APIRouter()

# Store for temporary 2FA sessions
# In production, this should be replaced with Redis or another distributed cache
TWO_FACTOR_SESSIONS = {}  # session_id -> {"user_id": user_id, "expires": datetime}

async def get_current_user_or_bypass(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> Optional[Dict]:
    """Get current user if token is provided, otherwise return None.
    
    This function is used for endpoints that can be accessed both with and without authentication.
    """
    if not token:
        return None
        
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            return None
    except JWTError:
        return None
        
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return None
        
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "role": user.role.value,
        "is_active": user.is_active
    }

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a new JWT access token."""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + (expires_delta or timedelta(minutes=15))
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)


def create_two_factor_session(user_id: int) -> str:
    """Create a temporary session for 2FA verification."""
    session_id = secrets.token_urlsafe(32)
    TWO_FACTOR_SESSIONS[session_id] = {
        "user_id": user_id,
        "expires": datetime.now(timezone.utc) + timedelta(minutes=5),
    }
    return session_id


def get_user_from_two_factor_session(session_id: str, db: Session) -> Optional[User]:
    """Get user from a 2FA session."""
    if session_id not in TWO_FACTOR_SESSIONS:
        return None

    session = TWO_FACTOR_SESSIONS[session_id]
    if session["expires"] < datetime.now(timezone.utc):
        # Session expired
        del TWO_FACTOR_SESSIONS[session_id]
        return None

    return db.query(User).filter(User.id == session["user_id"]).first()


@router.post("/register/", response_model=UserResponse)
async def register_user(
    *background_tasks: BackgroundTasks,
    user_data: UserCreate,
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> dict:
    """Register a new user.

    Creates a new user account with the provided information. The user wil receive
    an email verification link to activate their account.

    Args:
        background_tasks: FastAPI background tasks for sending emails
        user_data: User registration data including username, email, and password
        db: Database session

    Returns:
        The created user information (excluding sensitive data)
    """
    logger.debug(f"Processing registration request for username: {user_data.username}")

    # Validate password strength
    is_valid, message = validate_password(user_data.password)
    if not is_valid:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)

    # Validate email format
    if not validate_email(user_data.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid email format"
        )

    # Check if username already exists
    if db.query(User).filter(User.username == user_data.username).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Username already exists"
        )

    # Check if email already exists
    if db.query(User).filter(User.email == user_data.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Email already exists"
        )

    # Create user with default viewer role
    user = User(
        username=user_data.username,
        email=user_data.email,
        role=UserRole.VIEWER,
        full_name=user_data.full_name,
        bio=user_data.bio,
        is_active=True,
        email_verified=False,  # Email not verified yet
    )
    user.set_password(user_data.password)

    # Generate verification token
    verification_token = secrets.token_urlsafe(32)
    user.email_verification_token = verification_token
    user.email_verification_expires = datetime.utcnow() + timedelta(hours=24)

    db.add(user)
    db.commit()
    db.refresh(user)

    # Send verification email in the background
    background_tasks.add_task(
        send_verification_email, user.email, user.username, verification_token
    )

    logger.info(f"User registered successfully: {user.username}")

    return UserResponse(
        id=user.id,
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        bio=user.bio,
        role=user.role.value,
        is_active=user.is_active,
        created_at=user.created_at,
        last_login=user.last_login,
        two_factor_enabled=user.two_factor_enabled,
    )


@router.post("/verify-email/")
async def verify_email(
    verification_data: EmailVerificationRequest,
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> dict:
    """Verify user email address with the provided token.

    Args:
        verification_data: Email verification data including the token
        db: Database session

    Returns:
        Success message
    """
    user = (
        db.query(User)
        .filter(User.email_verification_token == verification_data.token)
        .first()
    )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid verification token"
        )

    # Check if token is expired
    if user.email_verification_expires < datetime.utcnow():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Verification token has expired",
        )

    # Mark email as verified
    user.email_verified = True
    user.email_verification_token = None
    user.email_verification_expires = None

    db.commit()

    logger.info(f"Email verified for user: {user.username}")

    return {"message": "Email verified successfully"}


@router.post("/resend-verification/")
async def resend_verification(
    *background_tasks: BackgroundTasks,
    email: EmailStr = Body(..., embed=True),
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> dict:
    """Resend email verification link.

    Args:
        background_tasks: FastAPI background tasks for sending emails
        email: User's email address
        db: Database session

    Returns:
        Success message
    """
    user = db.query(User).filter(User.email == email).first()

    if not user:
        # Return success even if user doesn't exist to prevent email enumeration
        return {
            "message": "If your email is registered, a verification link has been sent"
        }

    if user.email_verified:
        return {"message": "Email is already verified"}

    # Generate new verification token
    verification_token = secrets.token_urlsafe(32)
    user.email_verification_token = verification_token
    user.email_verification_expires = datetime.utcnow() + timedelta(hours=24)

    db.commit()

    # Send verification email in the background
    background_tasks.add_task(
        send_verification_email, user.email, user.username, verification_token
    )

    logger.info(f"Verification email resent for user: {user.username}")

    return {"message": "If your email is registered, a verification link has been sent"}


@router.post("/request-password-reset/")
async def request_password_reset(
    *background_tasks: BackgroundTasks,
    email: EmailStr = Body(..., embed=True),
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> dict:
    """Request a password reset link.

    Args:
        background_tasks: FastAPI background tasks for sending emails)
        email: User's email address
        db: Database session

    Returns:
        Success message
    """
    user = db.query(User).filter(User.email == email).first()

    if not user:
        # Return success even if user doesn't exist to prevent email enumeration
        return {
            "message": "If your email is registered, a password reset link has been sent"
        }

    # Generate password reset token
    reset_token = secrets.token_urlsafe(32)
    user.password_reset_token = reset_token
    user.password_reset_expires = datetime.utcnow() + timedelta(hours=1)

    db.commit()

    # Send password reset email in the background
    background_tasks.add_task(send_password_reset_email, user, reset_token)

    logger.info(f"Password reset requested for user: {user.username}")

    return {
        "message": "If your email is registered, a password reset link has been sent"
    }


@router.post("/reset-password/")
async def reset_password(
    reset_data: PasswordReset,
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> dict:
    """Reset user password with the provided token.

    Args:
        reset_data: Password reset data including token and new password
        db: Database session)

    Returns:
        Success message
    """
    user = db.query(User).filter(User.password_reset_token == reset_data.token).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid reset token"
        )

    # Check if token is expired
    if user.password_reset_expires < datetime.utcnow():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Reset token has expired"
        )

    # Validate new password
    is_valid, message = validate_password(reset_data.new_password)
    if not is_valid:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)

    # Update password
    user.set_password(reset_data.new_password)
    user.password_reset_token = None
    user.password_reset_expires = None

    # Reset failed login attempts and unlock account if locked
    user.failed_login_attempts = 0
    user.account_locked = False
    user.account_locked_at = None

    db.commit()

    logger.info(f"Password reset successful for user: {user.username}")

    return {"message": "Password reset successful"}


@router.post("/change-password/")
async def change_password(
    current_password: str = Body(...),
    new_password: str = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> dict:
    """Change user password.

    Args:
        current_password: User's current password
        new_password: User's new password
        db: Database session
        current_user: The authenticated user

    Returns:
        Success message
    """
    # Verify current password
    if not current_user.check_password(current_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Incorrect current password"
        )

    # Validate new password
    is_valid, message = validate_password(new_password)
    if not is_valid:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)

    # Update password
    current_user.set_password(new_password)
    current_user.password_changed_at = datetime.utcnow()

    db.commit()

    logger.info(f"Password changed for user: {current_user.username}")

    return {"message": "Password changed successfully"}


@router.post("/token/", response_model=LoginResponse)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> Dict:
    """Generate a new access token upon successful login."""
    logger.debug(f"Processing login request for username: {form_data.username}")

    user = db.query(User).filter(User.username == form_data.username).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if user.account_locked:
        raise HTTPException(
            detail="Account locked due to too many failed attempts",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.check_password(form_data.password):
        user.failed_login_attempts += 1
        if user.failed_login_attempts >= 5:
            user.account_locked = True
            user.account_locked_at = datetime.utcnow()
            logger.warning(
                f"Account locked for user: {user.username} after {user.failed_login_attempts} failed attempts"
            )
        db.commit()

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Reset failed login attempts on successful login
    user.failed_login_attempts = 0
    user.last_login = datetime.utcnow()
    db.commit()

    logger.info(f"Successful login for user: {user.username}")

    # Check if 2FA is enabled
    if user.two_factor_enabled:
        # Create a temporary session for 2FA verification
        session_id = create_two_factor_session(user.id)
        return {
            "requires_two_factor": True,
            "two_factor_session_id": session_id,
            "username": user.username,
            "role": user.role.value,
            "access_token": "",  # No token yet, will be provided after 2FA verification
        }

    # If 2FA is not enabled, generate token immediately
    access_token = create_access_token(
        data={"sub": user.username, "role": user.role.value},
        expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES),
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "username": user.username,
        "role": user.role.value,
        "requires_two_factor": False,
        "two_factor_session_id": None,
    }


@router.post("/verify-2fa/", response_model=LoginResponse)
async def verify_two_factor(
    data: TwoFactorVerify,
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> Dict:
    """Verify 2FA token and generate access token."""
    user = get_user_from_two_factor_session(data.session_id, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Verify the token
    is_valid = False
    if data.is_backup_code:
        is_valid = user.verify_backup_code(data.token)
    else:
        is_valid = user.verify_totp(data.token)

    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid verification code",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Clean up the temporary session
    if data.session_id in TWO_FACTOR_SESSIONS:
        del TWO_FACTOR_SESSIONS[data.session_id]

    # Generate access token
    access_token = create_access_token(
        data={"sub": user.username, "role": user.role.value},
        expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES),
    )

    # Save changes (in case a backup code was used)
    db.commit()

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "username": user.username,
        "role": user.role.value,
        "requires_two_factor": False,
        "two_factor_session_id": None,
    }


@router.post("/setup-2fa/")
async def setup_two_factor(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
) -> Dict:
    """Set up two-factor authentication for the user.

    Args:
        db: Database session
        current_user: The authenticated user

    Returns:
        Two-factor authentication setup information
    """
    # Check if 2FA is already enabled
    if current_user.two_factor_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication is already enabled",
        )

    # Generate a new TOTP secret
    secret, uri, backup_codes = current_user.generate_totp_secret()

    # Save the secret (but don't enable 2FA yet)
    current_user.two_factor_secret = secret
    db.commit()

    return {
        "secret": secret,
        "uri": uri,
        "backup_codes": backup_codes,
        "message": "Scan the QR code with your authenticator app and verify with a code to enable 2FA",
    }


@router.post("/enable-2fa/")
async def enable_two_factor(
    token: str = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Dict:
    """Enable two-factor authentication after setup.

    Args:
        token: Verification token from the authenticator app
        db: Database session
        current_user: The authenticated user

    Returns:
        Success message
    """
    # Check if 2FA is already enabled
    if current_user.two_factor_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication is already enabled",
        )

    # Check if a secret has been set up
    if not current_user.two_factor_secret:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication has not been set up",
        )

    # Verify the token
    if not current_user.verify_totp(token):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid verification code"
        )

    # Enable 2FA
    current_user.two_factor_enabled = True
    db.commit()

    logger.info(f"Two-factor authentication enabled for user: {current_user.username}")

    return {"message": "Two-factor authentication enabled successfully"}


@router.post("/disable-2fa/")
async def disable_two_factor(
    password: str = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Dict:
    """Disable two-factor authentication.

    Args:
        password: User's password for verification
        db: Database session
        current_user: The authenticated user

    Returns:
        Success message
    """
    # Check if 2FA is enabled
    if not current_user.two_factor_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication is not enabled",
        )

    # Verify password
    if not current_user.check_password(password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Incorrect password"
        )

    # Disable 2FA
    current_user.two_factor_enabled = False
    current_user.two_factor_secret = None
    current_user.backup_codes = None
    db.commit()

    logger.info(f"Two-factor authentication disabled for user: {current_user.username}")

    return {"message": "Two-factor authentication disabled successfully"}


@router.post("/regenerate-backup-codes/")
async def regenerate_backup_codes(
    password: str = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Dict:
    """Regenerate backup codes for two-factor authentication.

    Args:
        password: User's password for verification
        db: Database session
        current_user: The authenticated user

    Returns:
        New backup codes
    """
    # Check if 2FA is enabled
    if not current_user.two_factor_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Two-factor authentication is not enabled",
        )

    # Verify password
    if not current_user.check_password(password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Incorrect password"
        )

    # Generate new backup codes
    backup_codes = current_user.regenerate_backup_codes()
    db.commit()

    logger.info(f"Backup codes regenerated for user: {current_user.username}")

    return {"backup_codes": backup_codes}


@router.post("/unlock-account/")
async def unlock_account(
    *background_tasks: BackgroundTasks,
    email: EmailStr = Body(..., embed=True),
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> dict:
    """Request account unlock for a locked account.

    Args:
        background_tasks: FastAPI background tasks for sending emails
        email: User's email address
        db: Database session

    Returns:
        Success message
    """
    user = db.query(User).filter(User.email == email).first()

    if not user:
        # Return success even if user doesn't exist to prevent email enumeration
        return {
            "message": "If your account is locked, an unlock link has been sent to your email"
        }

    if not user.account_locked:
        return {"message": "Account is not locked"}

    # Generate unlock token
    unlock_token = secrets.token_urlsafe(32)
    user.account_unlock_token = unlock_token
    user.account_unlock_expires = datetime.now(timezone.utc) + timedelta(hours=1)

    db.commit()

    # Send unlock email in the background
    background_tasks.add_task(
        send_account_unlock_email, user.email, user.username, unlock_token
    )

    logger.info(f"Account unlock requested for user: {user.username}")

    return {
        "message": "If your account is locked, an unlock link has been sent to your email"
    }


@router.post("/confirm-unlock/")
async def confirm_account_unlock(
    token: str = Body(..., embed=True),
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit),  # Apply rate limiting
) -> dict:
    """Confirm account unlock with the provided token.

    Args:
        token: Account unlock token
        db: Database session

    Returns:
        Success message
    """
    user = db.query(User).filter(User.account_unlock_token == token).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid unlock token"
        )

    # Check if token is expired
    if user.account_unlock_expires < datetime.now(timezone.utc):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Unlock token has expired"
        )

    # Unlock account
    user.account_locked = False
    user.account_locked_at = None
    user.failed_login_attempts = 0
    user.account_unlock_token = None
    user.account_unlock_expires = None

    db.commit()

    logger.info(f"Account unlocked successfully for user: {user.username}")

    return {"message": "Account unlocked successfully"}


@router.get("/me/", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
) -> dict:
    """Get current user information.

    Args:
        current_user: The authenticated user

    Returns:
        Current user information
    """
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        bio=current_user.bio,
        role=current_user.role.value,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        last_login=current_user.last_login,
        two_factor_enabled=current_user.two_factor_enabled,
    )


@router.put("/me/", response_model=UserResponse)
async def update_current_user(
    user_update: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> dict:
    """Update current user information.

    Args:
        user_update: User update data
        db: Database session
        current_user: The authenticated user

    Returns:
        Updated user information
    """
    # Update allowed fields
    if "full_name" in user_update:
        current_user.full_name = user_update["full_name"]
    if "bio" in user_update:
        current_user.bio = user_update["bio"]
    if "email" in user_update:
        # Validate email format
        if not validate_email(user_update["email"]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid email format"
            )

        # Check if email already exists
        existing_user = db.query(User).filter(
            User.email == user_update["email"], User.id != current_user.id
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Email already exists"
            )

        current_user.email = user_update["email"]
        current_user.email_verified = False  # Require re-verification

    db.commit()
    db.refresh(current_user)

    logger.info(f"User profile updated for: {current_user.username}")

    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        bio=current_user.bio,
        role=current_user.role.value,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        last_login=current_user.last_login,
        two_factor_enabled=current_user.two_factor_enabled,
    )
