"""Utility functions for user authentication and management."""

import re
from datetime import datetime, timedelta
from typing import Optional, Tuple, List, Dict, Any, Union
import logging
import os
from sqlalchemy.orm import Session
from api.models.user import User, UserRole
from fastapi import HTTPException, status
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from pydantic import BaseModel, EmailStr
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from jose import jwt

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password requirements
MIN_PASSWORD_LENGTH = 8
REQUIRES_SPECIAL_CHAR = True
REQUIRES_NUMBER = True
REQUIRES_UPPERCASE = True
MAX_LOGIN_ATTEMPTS = 5

# Configure email settings
class EmailSettings(BaseModel):
    """Email configuration settings."""
    MAIL_USERNAME: str
    MAIL_PASSWORD: str
    MAIL_FROM: str  # Using str instead of EmailStr to avoid validation issues
    MAIL_PORT: int
    MAIL_SERVER: str
    MAIL_STARTTLS: bool = True
    MAIL_SSL_TLS: bool = False
    USE_CREDENTIALS: bool = True
    VALIDATE_CERTS: bool = True

# Initialize email settings
email_settings = EmailSettings(
    MAIL_USERNAME=os.getenv("MAIL_USERNAME", ""),
    MAIL_PASSWORD=os.getenv("MAIL_PASSWORD", ""),
    MAIL_FROM=os.getenv("MAIL_FROM", "<EMAIL>"),
    MAIL_PORT=int(os.getenv("MAIL_PORT", "587")),
    MAIL_SERVER=os.getenv("MAIL_SERVER", "smtp.gmail.com")
)
email_config = ConnectionConfig(**email_settings.model_dump())

logger = logging.getLogger(__name__)

# Email configuration
EMAIL_HOST = os.getenv("EMAIL_HOST", "smtp.gmail.com")
EMAIL_PORT = int(os.getenv("EMAIL_PORT", "587"))
EMAIL_USERNAME = os.getenv("EMAIL_USERNAME", "")
EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD", "")
EMAIL_FROM = os.getenv("EMAIL_FROM", "<EMAIL>")
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:3000")

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create a new JWT access token.
    
    Args:
        data: Data to encode in the token
        expires_delta: Optional expiration time delta
        
    Returns:
        Encoded JWT token as string
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def validate_password(password: str) -> Tuple[bool, str]:
    """Validate password strength."""
    if len(password) < MIN_PASSWORD_LENGTH:
        return False, f"Password must be at least {MIN_PASSWORD_LENGTH} characters long"

    if REQUIRES_SPECIAL_CHAR and not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
        return False, "Password must contain at least one special character"

    if REQUIRES_NUMBER and not re.search(r"\d", password):
        return False, "Password must contain at least one number"

    if REQUIRES_UPPERCASE and not re.search(r"[A-Z]", password):
        return False, "Password must contain at least one uppercase letter"

    return True, "Password is valid"

def validate_email(email: str) -> bool:
    """Validate email format using a simple regex pattern."""
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return bool(re.match(pattern, email))

def create_user(
    db: Session,
    username: str,
    email: str,
    password: str,
    role: str = UserRole.VIEWER,
    full_name: Optional[str] = None,
    bio: Optional[str] = None,
    is_active: bool = True
) -> Tuple[Optional[User], str]:
    """Create a new user."""
    # Validate input
    if not validate_email(email):
        return None, "Invalid email format"

    valid_pass, pass_msg = validate_password(password)
    if not valid_pass:
        return None, pass_msg

    # Check for existing user
    if db.query(User).filter(User.username == username).first():
        return None, "Username already exists"

    if db.query(User).filter(User.email == email).first():
        return None, "Email already exists"

    # Create user
    try:
        user = User(
            username=username,
            email=email,
            role=role,
            full_name=full_name,
            bio=bio,
            is_active=is_active
        )
        user.set_password(password)

        db.add(user)
        db.commit()
        db.refresh(user)
        return user, "User created successfully"
    except Exception as e:
        db.rollback()
        return None, f"Error creating user: {str(e)}"

def check_login_attempts(user: User) -> bool:
    """Check if user has exceeded maximum login attempts."""
    return user.failed_login_attempts >= MAX_LOGIN_ATTEMPTS

def reset_login_attempts(db: Session, user: User) -> None:
    """Reset failed login attempts counter."""
    user.failed_login_attempts = 0
    db.commit()

def update_last_login(db: Session, user: User) -> None:
    """Update user's last login timestamp."""
    user.last_login = datetime.utcnow()
    db.commit()

def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """Retrieve user by email address."""
    return db.query(User).filter(User.email == email).first()

def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """Retrieve user by username."""
    return db.query(User).filter(User.username == username).first()

def get_users_by_role(db: Session, role: str) -> List[User]:
    """Retrieve all users with specified role."""
    return db.query(User).filter(User.role == role).all()

def send_email(to_email: str, subject: str, html_content: str) -> bool:
    """Send an email.
    
    Args:
        to_email: Recipient email
        subject: Email subject
        html_content: HTML content of the email
        
    Returns:
        True if email was sent successfully, False otherwise
    """
    if not EMAIL_USERNAME or not EMAIL_PASSWORD:
        logger.warning("Email credentials not configured. Email not sent.")
        return False
    
    try:
        message = MIMEMultipart("alternative")
        message["Subject"] = subject
        message["From"] = EMAIL_FROM
        message["To"] = to_email
        
        html_part = MIMEText(html_content, "html")
        message.attach(html_part)
        
        with smtplib.SMTP(EMAIL_HOST, EMAIL_PORT) as server:
            server.starttls()
            server.login(EMAIL_USERNAME, EMAIL_PASSWORD)
            server.sendmail(EMAIL_FROM, to_email, message.as_string())
        
        logger.info(f"Email sent to {to_email}")
        return True
    except Exception as e:
        logger.error(f"Failed to send email: {str(e)}")
        return False

def send_verification_email(email: str, username: str, token: str) -> bool:
    """Send email verification link.
    
    Args:
        email: User's email address
        username: User's username
        token: Verification token
        
    Returns:
        True if email was sent successfully, False otherwise
    """
    verification_url = f"{FRONTEND_URL}/verify-email?token={token}"
    
    subject = "Verify Your Email Address"
    html_content = f"""
    <html>
    <body>
        <h2>Hello {username},</h2>
        <p>Thank you for registering. Please verify your email address by clicking the link below:</p>
        <p><a href="{verification_url}">Verify Email</a></p>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not create an account, please ignore this email.</p>
        <p>Best regards,<br>The Team</p>
    </body>
    </html>
    """
    
    return send_email(email, subject, html_content)

def send_password_reset_email(user: User, token: str) -> bool:
    """Send password reset link.
    
    Args:
        user: User object
        token: Reset token
        
    Returns:
        True if email was sent successfully, False otherwise
    """
    reset_url = f"{FRONTEND_URL}/reset-password?token={token}"
    
    subject = "Reset Your Password"
    html_content = f"""
    <html>
    <body>
        <h2>Hello {user.username},</h2>
        <p>We received a request to reset your password. Click the link below to reset it:</p>
        <p><a href="{reset_url}">Reset Password</a></p>
        <p>This link will expire in 1 hour.</p>
        <p>If you did not request a password reset, please ignore this email.</p>
        <p>Best regards,<br>The Team</p>
    </body>
    </html>
    """
    
    return send_email(user.email, subject, html_content)