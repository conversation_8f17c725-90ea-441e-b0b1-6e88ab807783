"""Database configuration and session management."""
from sqlalchemy import create_engine, MetaData, text, event
from sqlalchemy.orm import sessionmaker, DeclarativeBase
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError
import logging
import os
import sys
from typing import Generator, Optional, Tuple
from datetime import datetime

from api.utils.logging_config import get_logger

try:
    from flask_sqlalchemy import SQLAlchemy
except ImportError:
    SQLAlchemy = None

# Configure logging
logger = get_logger("db")
logger.setLevel(logging.DEBUG)

class DatabaseError(Exception):
    """Base class for database-related errors."""
    pass

# Database URL from environment
database_url = os.environ.get("DATABASE_URL")
if not database_url:
    logger.critical("DATABASE_URL environment variable is not set!")
    sys.exit(1)

# Define naming convention for constraints
convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

class Base(DeclarativeBase):
    """Base class for all SQLAlchemy models."""
    metadata = MetaData(naming_convention=convention)

# Initialize SQLAlchemy engine with connection pooling
engine = create_engine(
    database_url,
    pool_pre_ping=True,  # Enables connection health checks
    pool_recycle=300,    # Recycle connections every 5 minutes
    pool_size=20,        # Maximum number of connections in the pool
    max_overflow=10      # Allow up to 10 connections beyond pool_size
)

# Add query timing logging
@event.listens_for(Engine, "before_cursor_execute")
def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Log query timing - start."""
    conn.info.setdefault('query_start_time', []).append(datetime.utcnow())
    logger.debug(
        "Query starting",
        extra={
            "statement": statement,
            "parameters": parameters,
            "executemany": executemany
        }
    )

@event.listens_for(Engine, "after_cursor_execute")
def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Log query timing - end."""
    total_time = datetime.utcnow() - conn.info['query_start_time'].pop()
    logger.debug(
        "Query completed",
        extra={
            "statement": statement,
            "parameters": parameters,
            "duration": total_time.total_seconds(),
            "executemany": executemany
        }
    )

# Create SessionLocal class for FastAPI
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Flask-SQLAlchemy instance
if SQLAlchemy:
    db = SQLAlchemy(model_class=Base)
else:
    db = None
    logger.warning("Flask-SQLAlchemy not available, db instance will be None")

def verify_db_connection() -> Tuple[bool, Optional[str]]:
    """Verify database connection is working.
    
    Returns:
        Tuple of (success: bool, error_message: Optional[str])
    """
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            result.scalar()
            return True, None
    except SQLAlchemyError as e:
        error_msg = f"Database connection error: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

def get_db() -> Generator:
    """Get database session for FastAPI dependency injection."""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(
            "Database session error",
            extra={
                "error": str(e)
            },
            exc_info=True
        )
        raise
    finally:
        db.close()

def init_db(*, clean: bool = False) -> None:
    """Initialize database with all models.

    Args:
        clean: If True, drops all tables before recreating them
    """
    try:
        logger.info("Initializing database...")

        if clean:
            logger.info("Dropping all tables...")
            # Add checkfirst=True to handle non-existent tables gracefully
            Base.metadata.drop_all(bind=engine, checkfirst=True)
            logger.info("All tables dropped successfully")

        # Test database connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            result.scalar()
            logger.info("Database connection test successful")

        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(
            "Database initialization error",
            extra={
                "error": str(e)
            },
            exc_info=True
        )
        raise

# Export all necessary components
__all__ = ['Base', 'engine', 'get_db', 'init_db', 'SessionLocal', 'db', 'DatabaseError', 'verify_db_connection']