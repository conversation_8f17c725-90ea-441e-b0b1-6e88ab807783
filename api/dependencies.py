"""Dependencies for the FastAPI application.

Note: Authentication-related dependencies have been moved to api.auth.dependencies.
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from api.models.user import User

from api.auth.dependencies import (
    get_current_user,
    get_current_active_user,
    get_current_admin_user,
    admin_required,
    has_role,
    admin_only,
    operator_or_admin,
    has_permission,
    SECRET_KEY,
    ALGORITHM,
)

__all__ = [
    "get_current_user",
    "get_current_active_user",
    "get_current_admin_user",
    "admin_required",
    "has_role",
    "admin_only",
    "operator_or_admin",
    "has_permission",
    "SECRET_KEY",
    "ALG<PERSON><PERSON>H<PERSON>",
    "get_current_verified_user",
]

def get_current_verified_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Get the current active user with verified email.
    
    Args:
        current_user: The authenticated active user
        
    Returns:
        The authenticated active user with verified email
        
    Raises:
        HTTPException: If the user's email is not verified
    """
    if not current_user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email not verified"
        )
    return current_user