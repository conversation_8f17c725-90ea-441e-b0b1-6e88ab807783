"""Main FastAPI application module."""
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from fastapi import HTTPException, status
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi
import logging
import os
import sys
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError
import traceback
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from api.utils.logging_config import setup_logging, get_logger
from api.routes.user import router as user_router
from api.auth.router import router as auth_router  # Updated import path
from api.routes.v1 import router as v1_router  # Import consolidated v1 router
from api.routes.sessions import router as session_router  # Import session router
from api.routes.v1.two_factor import router as two_factor_router  # Import 2FA router
from api.routes.campaign import router as campaign_router  # Import campaign router
from api.routes.testcase import router as test_case_router  # Import testcase router
from api.routes.assessment import router as assessment_router  # Import assessment router
from api.routes.environment import router as environment_router  # Import environment router
from api.database import init_db, get_db
from api.utils.rate_limiter import init_rate_limiter  # Import rate limiter
from api.middleware.validation import get_validation_middleware, ValidationMiddleware  # Import validation middleware
from api.middleware.error_handler import ErrorHandlerMiddleware, register_error_handlers  # Import error handler middleware
from api.middleware.rate_limit import default_rate_limiter  # Import enhanced rate limiter
from api.endpoints.error_handling import router as error_handling_router  # Import error handling router
from api.endpoints.admin_interface import router as admin_interface_router  # Import admin interface router
from api.middleware.logging import RequestLoggingMiddleware
from api.routes.v1.error_handlers import exception_handlers

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Async context manager for FastAPI lifespan."""
    try:
        # Initialize centralized logging
        setup_logging(app_name="api")
        logger.info("Starting FastAPI application initialization")

        # Verify database URL
        database_url = os.environ.get("DATABASE_URL")
        if not database_url:
            raise ValueError("DATABASE_URL environment variable is not set")
        logger.info("Database URL configured: %s", database_url.split("@")[-1])  # Log only the host part

        # Initialize database
        try:
            logger.debug("Initializing database...")
            init_db()
            logger.info("Database initialization completed successfully")
        except SQLAlchemyError as e:
            logger.error(f"Database initialization error: {str(e)}")
            logger.error("Traceback: %s", traceback.format_exc())
            raise
            
        # Initialize rate limiter
        try:
            logger.debug("Initializing rate limiter...")
            await init_rate_limiter()
            logger.info("Rate limiter initialization completed successfully")
        except Exception as e:
            logger.error(f"Rate limiter initialization error: {str(e)}")
            logger.error("Traceback: %s", traceback.format_exc())
            logger.warning("Continuing without rate limiting")

        yield
    except Exception as e:
        logger.error(f"Application startup failed: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        raise
    finally:
        logger.info("Application shutdown")

def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    # Create the FastAPI app with lifespan support
    app = FastAPI(
        title="Regression Rigor API",
        description="""
        # Regression Rigor Cybersecurity Data Platform
        
        This API provides a comprehensive set of endpoints for managing cybersecurity data, 
        including user management, authentication, and integration with the MITRE ATT&CK framework.
        
        ## Key Features
        
        * **Secure Authentication**: JWT-based authentication with two-factor authentication support
        * **User Management**: Complete user lifecycle management with role-based access control
        * **MITRE ATT&CK Integration**: Access to MITRE ATT&CK techniques, tactics, and groups
        * **Rate Limiting**: Protection against abuse with tiered rate limiting
        * **Comprehensive Documentation**: Detailed API documentation with examples
        * **Error Handling**: Robust error handling with detailed error tracking
        
        ## Authentication
        
        Most endpoints require authentication. To authenticate, obtain a JWT token by using the `/api/v1/auth/token/` endpoint.
        If two-factor authentication is enabled, you'll need to complete the 2FA verification process.
        
        ## Rate Limiting
        
        This API implements rate limiting to protect against abuse. Different endpoints have different rate limits:
        
        * Standard endpoints: 100 requests per minute
        * Strict endpoints: 20 requests per minute
        * Authentication endpoints: 5 requests per minute
        * User-specific endpoints: 200 requests per minute per user
        
        When a rate limit is exceeded, the API will return a 429 Too Many Requests response.
        
        ## Input Validation
        
        All API endpoints implement strict input validation. Invalid inputs will result in a 422 Unprocessable Entity response
        with detailed error messages. Make sure to follow the schema definitions for all requests.
        
        ## Error Handling
        
        The API implements comprehensive error handling with detailed error tracking. All errors are logged and can be
        viewed through the error handling endpoints. Different types of errors (validation, authentication, database, etc.)
        are handled appropriately with meaningful error messages.
        """,
        version="1.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        exception_handlers=exception_handlers,
        lifespan=lifespan,
        contact={
            "name": "Regression Rigor Support",
            "url": "https://example.com/support",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "Proprietary",
            "url": "https://example.com/license",
        },
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # TODO: Configure this for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add rate limiting middleware
    try:
        app.middleware("http")(default_rate_limiter)
        logger.info("Rate limiting middleware added")
    except Exception as e:
        logger.error(f"Error adding rate limiting middleware: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        logger.warning("Continuing without rate limiting middleware")
    
    # Add error handling middleware
    try:
        app.add_middleware(ErrorHandlerMiddleware)
        register_error_handlers(app)
        logger.info("Error handling middleware added")
    except Exception as e:
        logger.error(f"Error adding error handling middleware: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        logger.warning("Continuing without error handling middleware")
    
    # Add validation middleware
    try:
        app.add_middleware(ValidationMiddleware)
        logger.info("Validation middleware added")
    except Exception as e:
        logger.error(f"Error adding validation middleware: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        logger.warning("Continuing without validation middleware")

    # Add request logging middleware
    app.add_middleware(RequestLoggingMiddleware)

    # Include routers
    try:
        # Authentication router
        app.include_router(
            auth_router,
            prefix="/api",
            tags=["Authentication"]
        )

        # User management router
        app.include_router(
            user_router,
            prefix="/api",
            tags=["Users"]
        )

        # Session management router
        app.include_router(
            session_router,
            prefix="/api",
            tags=["Sessions"]
        )
        
        # Two-factor authentication router
        app.include_router(
            two_factor_router,
            prefix="/api",
            tags=["Two-Factor Authentication"]
        )
        
        # Assessment management router
        app.include_router(
            assessment_router,
            prefix="/api/v1",
            tags=["Assessments"]
        )
        
        # Campaign management router
        app.include_router(
            campaign_router,
            prefix="/api/v1",
            tags=["Campaigns"]
        )
        
        # Environment management router
        app.include_router(
            environment_router,
            prefix="/api/v1",
            tags=["Environments"]
        )
        
        # Error handling router
        app.include_router(
            error_handling_router,
            prefix="/api",
            tags=["Error Handling"]
        )
        
        # Admin interface router
        app.include_router(
            admin_interface_router,
            prefix="/api",
            tags=["Admin Interface"]
        )

        # Include test case router
        app.include_router(
            test_case_router,
            prefix="/api/v1",
            tags=["Test Cases"]
        )

        # Include all v1 routes
        app.include_router(v1_router, prefix="/api")

        # Add campaign, testcase, and assessment routers
        app.include_router(campaign_router, tags=["Campaigns"])
        app.include_router(test_case_router, tags=["Test Cases"])
        app.include_router(assessment_router, tags=["Assessments"])

        logger.info("All routers included successfully")
    except Exception as e:
        logger.error(f"Error including routers: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        raise

    # Custom OpenAPI schema
    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema
        
        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
            contact=app.contact,
            license_info=app.license_info,
        )
        
        # Add security schemes
        openapi_schema["components"]["securitySchemes"] = {
            "OAuth2PasswordBearer": {
                "type": "oauth2",
                "flows": {
                    "password": {
                        "tokenUrl": "/api/v1/auth/token",
                        "scopes": {
                            "admin": "Admin access",
                            "analyst": "Analyst access",
                            "viewer": "Viewer access"
                        }
                    }
                }
            },
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT"
            }
        }
        
        # Add global security requirement
        openapi_schema["security"] = [{"BearerAuth": []}]
        
        # Add custom examples and descriptions
        for path in openapi_schema["paths"]:
            for method in openapi_schema["paths"][path]:
                if method.lower() in ["get", "post", "put", "delete", "patch"]:
                    # Add rate limiting information
                    if "description" in openapi_schema["paths"][path][method]:
                        openapi_schema["paths"][path][method]["description"] += "\n\n**Rate Limiting**: This endpoint is subject to rate limiting."
                    else:
                        openapi_schema["paths"][path][method]["description"] = "**Rate Limiting**: This endpoint is subject to rate limiting."
                    
                    # Add validation information
                    if "description" in openapi_schema["paths"][path][method]:
                        openapi_schema["paths"][path][method]["description"] += "\n\n**Input Validation**: All inputs are validated. Invalid inputs will result in a 422 response."
                    else:
                        openapi_schema["paths"][path][method]["description"] = "**Input Validation**: All inputs are validated. Invalid inputs will result in a 422 response."
                    
                    # Add error handling information
                    if "description" in openapi_schema["paths"][path][method]:
                        openapi_schema["paths"][path][method]["description"] += "\n\n**Error Handling**: All errors are logged and can be viewed through the error handling endpoints."
                    else:
                        openapi_schema["paths"][path][method]["description"] = "**Error Handling**: All errors are logged and can be viewed through the error handling endpoints."
        
        app.openapi_schema = openapi_schema
        return app.openapi_schema
    
    app.openapi = custom_openapi
    
    # Custom documentation endpoints
    @app.get("/api/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        return get_swagger_ui_html(
            openapi_url="/openapi.json",
            title=f"{app.title} - Swagger UI",
            oauth2_redirect_url="/api/docs/oauth2-redirect",
            swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
            swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
            init_oauth={
                "clientId": "regression-rigor-client",
                "appName": "Regression Rigor API",
                "usePkceWithAuthorizationCodeGrant": True,
            }
        )
    
    @app.get("/api/redoc", include_in_schema=False)
    async def redoc_html():
        return get_redoc_html(
            openapi_url="/openapi.json",
            title=f"{app.title} - ReDoc",
            redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js",
            redoc_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
        )

    # Note: We don't need to add custom exception handlers here as they are handled by the error handling middleware

    return app

# Create the application instance
try:
    logger.info("Creating FastAPI application instance")
    app = create_app()
    logger.info("FastAPI application instance created successfully")
except Exception as e:
    logger.error(f"Failed to create FastAPI application: {str(e)}")
    logger.error("Traceback: %s", traceback.format_exc())
    raise

@app.on_event("startup")
async def startup_event():
    """Initialize application on startup."""
    try:
        logger.info("Starting application...")
        init_db()
        logger.info("Application started successfully")
    except Exception as e:
        logger.error(
            "Application startup failed",
            extra={
                "error": str(e)
            },
            exc_info=True
        )
        raise

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 5001))
    logger.info(f"Starting server on port {port}")
    uvicorn.run("api.main:app", host="0.0.0.0", port=port, reload=True)