"""
Models package for the cybersecurity data platform.
Contains all database models used in the application.
"""

# Base models
from api.models.user import User, UserRole
from api.models.user_preferences import UserPreference

# Session models
from api.models.session import UserSession, DeviceInfo

# Logging models
from api.models.audit import AuditLog

# Hierarchical data models
from api.models.base import AssessmentDB
from api.models.assessment import Assessment, TestExecution
from api.models.campaign import CampaignDB
from api.models.organization import OrganizationDB
from api.models.database.test_case import TestCaseDB
from api.models.database.admin_interface import AdminAuditLog, AdminNotification
from api.models.error_log import ErrorLog

# MITRE models
from mitreattack.stix20 import MitreAttackData
from api.models.mitre import MitreTechnique, MitreTactic, MitreVersion, TechniqueScore
from api.models.relationships import MitreRelationship

# ATLAS models
from api.models.atlas import AtlasTechnique, AtlasTactic, AtlasRelationship

# STIX models
from api.models.stix import StixDB, StixObject, StixBundle

# Export all models that are currently in use
__all__ = [
    # Base models
    'User',
    'UserRole',
    'UserPreference',
    
    # Session models
    'UserSession',
    'DeviceInfo',
    
    # Logging models
    'AuditLog',
    
    # Hierarchical data models
    'AssessmentDB',
    'Assessment',
    'TestExecution',
    'CampaignDB',
    'OrganizationDB',
    'TestCaseDB',
    
    # MITRE models
    'MitreAttackData',
    'MitreTechnique',
    'MitreTactic',
    'MitreVersion',
    'TechniqueScore',
    
    # ATLAS models
    'AtlasTechnique',
    'AtlasTactic',
    
    # STIX models
    'StixDB',
    'StixObject',
    'StixBundle',
    
    # New imports
    'AdminAuditLog',
    'AdminNotification',
    'ErrorLog',
    'MitreRelationship'
]