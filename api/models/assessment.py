"""
Database models for assessments and test executions.

This module contains the SQLAlchemy models for assessments and test executions,
which are used to track security testing activities and their outcomes.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from api.database import Base
from api.models.base import AssessmentDB, VersionMixin
from api.models.mixins import SoftDeleteMixin

# Re-export AssessmentDB as Assessment for backward compatibility
Assessment = AssessmentDB

class TestExecution(Base):
    """Database model for test case executions within assessments."""
    __tablename__ = "test_executions"

    id = Column(Integer, primary_key=True, index=True)
    test_case_id = Column(Integer, ForeignKey("test_cases.id"))
    assessment_id = Column(Integer, ForeignKey("assessments.id"))
    result = Column(String, default="pending")  # pending, pass, fail, partial, blocked, not_applicable
    notes = Column(Text, nullable=True)
    evidence = Column(JSON, nullable=True)  # JSON data for evidence (links, screenshots, etc.)
    executed_by = Column(String, ForeignKey("users.id"))
    executed_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    test_case = relationship("TestCase", back_populates="executions")
    assessment = relationship("AssessmentDB", back_populates="test_executions")
    executor = relationship("User", foreign_keys=[executed_by], back_populates="executed_tests")

    def __repr__(self):
        return f"<TestExecution id={self.id} test_case_id={self.test_case_id} result={self.result}>"