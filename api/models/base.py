"""Base models for the application.

This module provides the foundational database models and mixins for the cybersecurity
data platform. It establishes common functionality like versioning and soft deletion
that can be used across all models in the application.

The module uses SQLAlchemy 2.0 style ORM patterns for all database interactions.
"""
from datetime import datetime
from typing import Optional, List, Any
from sqlalchemy import text, <PERSON><PERSON>n, Integer, String, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship, Session
from sqlalchemy.orm import DeclarativeBase, declared_attr
from api.database import Base
from sqlalchemy.sql import func

# Forward references for type hints
from api.models.testcase_chaining import TestcaseConditionDB
from api.models.mixins import VersionMixin

class CampaignDB(Base, VersionMixin):
    """Database model for security testing campaigns."""
    __tablename__ = "cl_campaign"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]
    status: Mapped[str] = mapped_column(server_default="active")

    # Relationships
    test_cases: Mapped[List["TestCaseDB"]] = relationship(
        back_populates="campaign",
        cascade="all, delete-orphan"
    )
    organizations: Mapped[List["OrganizationDB"]] = relationship(
        secondary="cl_campaign_to_organization",
        back_populates="campaigns"
    )
    assessments: Mapped[List["AssessmentDB"]] = relationship(
        secondary="assessment_to_campaign",
        back_populates="campaigns"
    )

class OrganizationDB(Base, VersionMixin):
    """Database model for organizations."""
    __tablename__ = "cl_organization"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]

    # Relationships
    campaigns: Mapped[List["CampaignDB"]] = relationship(
        secondary="cl_campaign_to_organization",
        back_populates="organizations"
    )

class CampaignToOrganizationDB(Base):
    """Association table for campaign-organization many-to-many relationship."""
    __tablename__ = "cl_campaign_to_organization"

    campaign_id: Mapped[int] = mapped_column(
        ForeignKey("cl_campaign.id", ondelete="CASCADE"),
        primary_key=True
    )
    organization_id: Mapped[int] = mapped_column(
        ForeignKey("cl_organization.id", ondelete="CASCADE"),
        primary_key=True
    )

class TestCaseDB(Base, VersionMixin):
    """Database model for individual security test cases."""
    __tablename__ = "test_cases_base"  # Changed from test_cases to avoid conflict

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]
    campaign_id: Mapped[int] = mapped_column(
        ForeignKey("cl_campaign.id", ondelete="CASCADE")
    )
    expected_result: Mapped[str] = mapped_column(nullable=False)
    actual_result: Mapped[Optional[str]]
    status: Mapped[str] = mapped_column(server_default="pending")
    created_by: Mapped[str] = mapped_column(ForeignKey("users.id"), nullable=True)

    # Relationships
    campaign: Mapped["CampaignDB"] = relationship(back_populates="test_cases")
    creator: Mapped["User"] = relationship(foreign_keys=[created_by])
    conditions: Mapped[List["TestcaseConditionDB"]] = relationship(
        back_populates="testcase",
        cascade="all, delete-orphan"
    )

class AssessmentDB(Base, VersionMixin):
    """Database model for security assessments."""
    __tablename__ = "assessments"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]
    target_system: Mapped[str] = mapped_column(nullable=False)
    assessment_type: Mapped[str] = mapped_column(nullable=False)  # e.g., "vulnerability", "penetration", "code review"
    start_date: Mapped[datetime] = mapped_column(default=func.now())
    end_date: Mapped[Optional[datetime]]
    status: Mapped[str] = mapped_column(server_default="pending")  # pending, in_progress, completed, reviewed, cancelled
    environment_id: Mapped[Optional[int]] = mapped_column(ForeignKey("environments.id"))
    
    # User who created the assessment
    created_by: Mapped[str] = mapped_column(ForeignKey("users.id"))
    
    # Relationships
    campaigns: Mapped[List["CampaignDB"]] = relationship(
        secondary="assessment_to_campaign",
        back_populates="assessments"
    )
    creator: Mapped["User"] = relationship(foreign_keys=[created_by])
    environment: Mapped[Optional["Environment"]] = relationship(back_populates="assessments")
    test_executions: Mapped[List["TestExecution"]] = relationship(
        back_populates="assessment",
        cascade="all, delete-orphan"
    )

class AssessmentToCampaignDB(Base):
    """Association table for assessment-campaign many-to-many relationship."""
    __tablename__ = "assessment_to_campaign"

    assessment_id: Mapped[int] = mapped_column(
        ForeignKey("assessments.id", ondelete="CASCADE"),
        primary_key=True
    )
    campaign_id: Mapped[int] = mapped_column(
        ForeignKey("cl_campaign.id", ondelete="CASCADE"),
        primary_key=True
    )