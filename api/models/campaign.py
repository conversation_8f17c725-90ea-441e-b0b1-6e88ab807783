"""
Campaign model for the RegressionRigor platform.

This module defines the Campaign model and related database models for
managing security testing campaigns.
"""
from datetime import datetime
from typing import List, Optional
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, ForeignKey, Table, Boolean
from sqlalchemy.orm import relationship, Session, Mapped, mapped_column
from sqlalchemy.sql import func

from api.database import Base
from api.models.mixins import SoftDeleteMixin, VersionMixin


class CampaignDB(Base, SoftDeleteMixin):
    """
    Campaign model for security testing campaigns.
    
    A campaign represents a collection of related security tests that are executed
    together to assess a specific target or system.
    
    Attributes:
        id: Unique identifier for the campaign
        name: Name of the campaign
        description: Detailed description of the campaign
        status: Current status of the campaign (draft, active, completed, archived)
        start_date: Planned start date for the campaign
        end_date: Planned end date for the campaign
        assessment_id: ID of the assessment this campaign belongs to
        created_by: ID of the user who created the campaign
        created_at: Timestamp when the campaign was created
        updated_at: Timestamp when the campaign was last updated
        deleted_at: Timestamp when the campaign was soft-deleted (if applicable)
    """
    __tablename__ = "campaigns"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(50), nullable=False, default="draft")  # draft, active, completed, archived
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    assessment_id = Column(Integer, ForeignKey("assessments.id"), nullable=True)
    created_by = Column(String, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    creator = relationship("User", back_populates="campaigns")
    test_cases = relationship("CampaignTestCase", back_populates="campaign")
    assessment = relationship("Assessment", back_populates="campaigns", foreign_keys=[assessment_id])
    
    def __repr__(self):
        """Return string representation of the campaign."""
        return f"<Campaign(id={self.id}, name='{self.name}', status='{self.status}')>"
    
    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status,
            "start_date": self.start_date.isoformat() if self.start_date else None,
            "end_date": self.end_date.isoformat() if self.end_date else None,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_deleted": self.is_deleted
        }
    
    def soft_delete(self, db: Session):
        """
        Soft delete the campaign.
        
        Args:
            db: Database session
        """
        self.deleted_at = datetime.utcnow()
        db.commit()
        return self


class CampaignTestCase(Base):
    """Model for campaign-test case association."""
    __tablename__ = "campaign_test_cases"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True, index=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=False)
    test_case_id = Column(Integer, ForeignKey("test_cases.id"), nullable=False)
    status = Column(String(50), nullable=False, default="pending")  # pending, in_progress, completed, failed
    assigned_to = Column(String, ForeignKey("users.id"))
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    campaign = relationship("CampaignDB", back_populates="test_cases")
    test_case = relationship("TestCase", back_populates="campaigns")
    assignee = relationship("User", back_populates="assigned_test_cases") 