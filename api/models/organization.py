"""Organization-related models."""
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, ForeignKey, JSON, Table
from sqlalchemy.orm import relationship
from datetime import datetime
from api.database import Base
from api.models.mixins import SoftDeleteMixin
from api.models.relationships import campaign_to_organization

class OrganizationDB(Base, SoftDeleteMixin):
    """Organization model."""
    __tablename__ = "organizations"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(String(1000))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    campaigns = relationship("CampaignDB", secondary=campaign_to_organization, back_populates="organizations")

class CLCampaign(Base, SoftDeleteMixin):
    """Campaign model for organizing related security activities."""
    __tablename__ = "cl_campaign"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(String(2000))
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    status = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    meta_data = Column(JSON)  # Additional metadata

    # Relationships
    organizations = relationship("OrganizationDB", secondary=campaign_to_organization, back_populates="campaigns")