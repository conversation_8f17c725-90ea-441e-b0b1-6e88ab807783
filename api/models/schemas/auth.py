"""Authentication schemas for the RegressionRigor API."""

from pydantic import BaseModel, EmailStr
from typing import Optional

class UserCreate(BaseModel):
    """Schema for creating a new user."""
    email: EmailStr
    password: str
    full_name: str

class UserUpdate(BaseModel):
    """Schema for updating a user."""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    is_superuser: Optional[bool] = None

class UserResponse(BaseModel):
    """Schema for user response."""
    id: int
    email: EmailStr
    full_name: str
    is_active: bool
    is_verified: bool
    is_superuser: bool

class LoginResponse(BaseModel):
    """Schema for login response."""
    access_token: str
    token_type: str = "bearer"
    user: UserResponse

class PasswordResetRequest(BaseModel):
    """Schema for password reset request."""
    email: EmailStr

class PasswordReset(BaseModel):
    """Schema for password reset."""
    token: str
    new_password: str

class EmailVerificationRequest(BaseModel):
    """Schema for email verification request."""
    token: str

class UnlockAccountRequest(BaseModel):
    """Schema for unlocking a user account."""
    email: EmailStr 