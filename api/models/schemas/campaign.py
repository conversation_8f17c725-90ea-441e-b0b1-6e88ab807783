"""
Pydantic schemas for Campaign API.

This module contains Pydantic models for validating and serializing data
related to campaigns in the RegressionRigor platform.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, validator


class CampaignStatus(str, Enum):
    """Enumeration of possible campaign statuses."""
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class CampaignBase(BaseModel):
    """
    Base model for campaign data validation.
    
    Contains the common attributes shared between campaign creation
    and response models.
    
    Attributes:
        name: Name of the campaign
        description: Detailed description of the campaign
        status: Current status of the campaign
        start_date: Planned start date for the campaign
        end_date: Planned end date for the campaign
        assessment_id: ID of the assessment this campaign belongs to
    """
    name: str = Field(..., min_length=3, max_length=100, description="Name of the campaign")
    description: Optional[str] = Field(None, max_length=5000, description="Detailed description of the campaign")
    status: CampaignStatus = Field(default=CampaignStatus.DRAFT, description="Current status of the campaign")
    start_date: Optional[datetime] = Field(None, description="Planned start date for the campaign")
    end_date: Optional[datetime] = Field(None, description="Planned end date for the campaign")
    assessment_id: Optional[int] = Field(None, description="ID of the assessment this campaign belongs to")
    
    @validator('end_date')
    def end_date_after_start_date(cls, v, values):
        """Validate that end_date is after start_date if both are provided."""
        if v and 'start_date' in values and values['start_date'] and v < values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v


class CampaignCreate(CampaignBase):
    """
    Model for campaign creation requests.
    
    Inherits from CampaignBase and doesn't add additional fields, but
    is kept separate to maintain API flexibility for future extensions.
    """
    pass


class CampaignUpdate(BaseModel):
    """
    Model for campaign update requests.
    
    Contains all fields that can be updated for a campaign, all of which
    are optional to allow partial updates.
    
    Attributes:
        name: Name of the campaign
        description: Detailed description of the campaign
        status: Current status of the campaign
        start_date: Planned start date for the campaign
        end_date: Planned end date for the campaign
        assessment_id: ID of the assessment this campaign belongs to
    """
    name: Optional[str] = Field(None, min_length=3, max_length=100, description="Name of the campaign")
    description: Optional[str] = Field(None, max_length=5000, description="Detailed description of the campaign")
    status: Optional[CampaignStatus] = Field(None, description="Current status of the campaign")
    start_date: Optional[datetime] = Field(None, description="Planned start date for the campaign")
    end_date: Optional[datetime] = Field(None, description="Planned end date for the campaign")
    assessment_id: Optional[int] = Field(None, description="ID of the assessment this campaign belongs to")
    
    @validator('end_date')
    def end_date_after_start_date(cls, v, values):
        """Validate that end_date is after start_date if both are provided."""
        if v and 'start_date' in values and values['start_date'] and v < values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v


class Campaign(CampaignBase):
    """
    Complete campaign model including database-generated fields.
    
    Extends CampaignBase to include the id and timestamps that are
    generated when the campaign is stored in the database.
    
    Attributes:
        id: Unique identifier for the campaign
        created_by: ID of the user who created the campaign
        created_at: Timestamp when the campaign was created
        updated_at: Timestamp when the campaign was last updated
        deleted_at: Timestamp when the campaign was soft-deleted, or None if not deleted
    """
    id: int = Field(..., description="Unique identifier for the campaign")
    created_by: int = Field(..., description="ID of the user who created the campaign")
    created_at: datetime = Field(..., description="Timestamp when the campaign was created")
    updated_at: datetime = Field(..., description="Timestamp when the campaign was last updated")
    deleted_at: Optional[datetime] = Field(None, description="Timestamp when the campaign was soft-deleted")
    
    class Config:
        """Pydantic configuration."""
        orm_mode = True


class CampaignWithTestCases(Campaign):
    """
    Campaign model with associated test cases.
    
    Extends Campaign to include the list of associated test cases.
    
    Attributes:
        test_cases: List of test cases associated with the campaign
    """
    test_cases: List[Dict[str, Any]] = Field(default=[], description="List of test cases associated with the campaign")


class TestCaseAssignment(BaseModel):
    """
    Model for assigning test cases to a campaign.
    
    Attributes:
        test_case_ids: List of test case IDs to assign to the campaign
    """
    test_case_ids: List[int] = Field(..., description="List of test case IDs to assign to the campaign")


class CampaignTestCaseUpdate(BaseModel):
    """
    Model for updating a test case in a campaign.
    
    Attributes:
        status: Current status of the test case in the campaign
        priority: Priority level of the test case
        notes: Additional notes about the test case
        assigned_to: ID of the user assigned to the test case
        due_date: Due date for completing the test case
    """
    status: Optional[str] = Field(None, description="Current status of the test case in the campaign")
    priority: Optional[str] = Field(None, description="Priority level of the test case")
    notes: Optional[str] = Field(None, description="Additional notes about the test case")
    assigned_to: Optional[int] = Field(None, description="ID of the user assigned to the test case")
    due_date: Optional[datetime] = Field(None, description="Due date for completing the test case")


class CampaignSummary(BaseModel):
    """
    Summary of a campaign's test cases and assessments.
    
    Attributes:
        total_test_cases: Total number of test cases in the campaign
        total_assessments: Total number of assessments in the campaign
        test_case_status: Count of test cases by status
        assessment_status: Count of assessments by status
    """
    total_test_cases: int = Field(..., description="Total number of test cases in the campaign")
    total_assessments: int = Field(..., description="Total number of assessments in the campaign")
    test_case_status: Dict[str, int] = Field(..., description="Count of test cases by status")
    assessment_status: Dict[str, int] = Field(..., description="Count of assessments by status")
    completion_percentage: float = Field(..., description="Percentage of completed assessments")
    
    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "total_test_cases": 25,
                "total_assessments": 3,
                "test_case_status": {
                    "draft": 5,
                    "active": 15,
                    "deprecated": 5
                },
                "assessment_status": {
                    "pending": 1,
                    "in_progress": 1,
                    "completed": 1,
                    "reviewed": 0
                },
                "completion_percentage": 33.33
            }
        } 