"""
Pydantic schemas for MITRE ATT&CK data validation.

This module defines the data validation models for MITRE ATT&CK data.
These models are used for validating request and response data.
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

class TechniqueBase(BaseModel):
    """Base model for MITRE ATT&CK technique data validation."""
    technique_id: str = Field(..., description="MITRE ATT&CK technique ID (e.g., T1566)")
    name: str = Field(..., description="Name of the technique")
    description: Optional[str] = Field(None, description="Detailed description of the technique")
    detection: Optional[str] = Field(None, description="Detection strategies")
    platforms: Optional[List[str]] = Field(default=[], description="Applicable platforms")
    extra_data: Optional[Dict[str, Any]] = Field(default={}, description="Additional MITRE data")

class TechniqueCreate(TechniqueBase):
    """Model for technique creation requests."""
    version_id: int = Field(..., description="ID of the MITRE version this technique belongs to")

class TechniqueResponse(TechniqueBase):
    """Complete technique model including database-generated fields."""
    id: int = Field(..., description="Database ID of the technique")
    version_id: int = Field(..., description="ID of the MITRE version this technique belongs to")
    created_at: datetime = Field(..., description="Timestamp when the technique was created")
    updated_at: datetime = Field(..., description="Timestamp of the last technique update")
    deleted_at: Optional[datetime] = Field(None, description="Timestamp when the technique was deleted")

    class Config:
        """Pydantic configuration."""
        from_attributes = True 