"""
Pydantic schemas for two-factor authentication.

This module defines the data validation models for two-factor authentication.
These models are used for validating request and response data.
"""
from typing import List, Optional
from pydantic import BaseModel, Field

class TwoFactorSetup(BaseModel):
    """Response model for 2FA setup."""
    secret: str = Field(..., description="The TOTP secret key")
    qr_code_uri: str = Field(..., description="The QR code URI for scanning with authenticator apps")
    backup_codes: List[str] = Field(..., description="List of backup codes for account recovery")

class TwoFactorEnable(BaseModel):
    """Request model for enabling 2FA."""
    token: str = Field(..., description="The TOTP token for verification")

class TwoFactorVerify(BaseModel):
    """Request model for verifying 2FA."""
    token: str = Field(..., description="The TOTP token or backup code")

class TwoFactorDisable(BaseModel):
    """Request model for disabling 2FA."""
    password: str = Field(..., description="The user's password for verification")

class TwoFactorStatus(BaseModel):
    """Response model for 2FA status."""
    enabled: bool = Field(..., description="Whether 2FA is enabled")
    backup_codes_remaining: int = Field(..., description="Number of backup codes remaining") 