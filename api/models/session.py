"""User session and device tracking models."""
from datetime import datetime
import uuid
from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from api.database import Base

class DeviceInfo(Base):
    """Store device fingerprint information."""
    __tablename__ = 'device_info'
    __table_args__ = {"extend_existing": True}

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_agent = Column(String, nullable=False)
    ip_address = Column(String, nullable=False)
    device_type = Column(String)  # mobile, desktop, tablet
    os_info = Column(String)
    browser_info = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_seen = Column(DateTime(timezone=True), default=datetime.utcnow)

    # Relationships
    sessions = relationship("UserSession", back_populates="device")

class UserSession(Base):
    """Track user sessions across devices."""
    __tablename__ = 'user_sessions'
    __table_args__ = {"extend_existing": True}

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    device_id = Column(String, ForeignKey('device_info.id', ondelete='SET NULL'), nullable=True)
    session_token = Column(String, unique=True, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_activity = Column(DateTime(timezone=True), default=datetime.utcnow)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    logout_at = Column(DateTime(timezone=True), nullable=True)
    ip_address = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)

    # Relationships
    user = relationship("User", back_populates="sessions")
    device = relationship("DeviceInfo", back_populates="sessions")

    def terminate(self):
        """Terminate the session."""
        self.is_active = False
        self.logout_at = datetime.utcnow()

    def update_activity(self):
        """Update the last activity timestamp."""
        self.last_activity = datetime.utcnow()

    @property
    def is_expired(self):
        """Check if the session has expired."""
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self):
        """Check if the session is valid (active and not expired)."""
        return self.is_active and not self.is_expired
