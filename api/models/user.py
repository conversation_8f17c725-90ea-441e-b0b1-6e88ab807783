"""User model definition for the FastAPI cybersecurity data platform."""

import enum
import json
import secrets
import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional, List
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum as SQLEnum, Foreign<PERSON>ey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from passlib.context import CryptContext
from flask_login import UserMixin
import enum
import pyotp
from flask_login import UserMixin
from passlib.context import CryptContext
from sqlalchemy import <PERSON>olean, Column, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from api.database import Base
from api.models.session import UserSession
from api.models.user_preferences import UserPreference
from api.models.user_activity import UserActivity
from api.models.database.admin_interface import AdminAuditLog, AdminNotification
from api.models.error_log import ErrorLog
from api.models.audit import AuditLog
from api.models.base import AssessmentDB as Assessment

# Configure password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class UserRole(str, enum.Enum):
    """Enumeration of available user roles."""
    ADMIN = "admin"
    OPERATOR = "operator"
    ANALYST = "analyst"
    VIEWER = "viewer"

class User(UserMixin, Base):
    """User model for authentication and authorisation."""

    __tablename__ = "users"
    __table_args__ = {"extend_existing": True}

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String(length=256), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    email_verified = Column(Boolean, default=False)

    # Enhanced user profile fields
    role = Column(SQLEnum(UserRole), nullable=False, default=UserRole.VIEWER)
    full_name = Column(String(length=100))
    bio = Column(Text, nullable=True)
    last_login = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts = Column(Integer, default=0)
    password_changed_at = Column(DateTime(timezone=True), nullable=True)

    # Security related fields
    account_locked = Column(Boolean, default=False)
    account_locked_at = Column(DateTime(timezone=True), nullable=True)
    last_failed_login = Column(DateTime(timezone=True), nullable=True)
    password_reset_token = Column(String(256), nullable=True)
    password_reset_expires = Column(DateTime(timezone=True), nullable=True)
    account_unlock_token = Column(String(256), nullable=True)
    account_unlock_expires = Column(DateTime(timezone=True), nullable=True)

    # Two-factor authentication fields
    two_factor_enabled = Column(Boolean, default=False)
    two_factor_secret = Column(String(32), nullable=True)
    backup_codes = Column(String(512), nullable=True)  # Comma-separated list of hashed backup codes

    # Session management relationship
    sessions = relationship(
        "UserSession",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="selectin",
    )

    # Relationship to preferences
    preferences = relationship(
        "UserPreference",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="selectin",
    )

    # Admin interface relationships
    audit_logs = relationship(
        "AuditLog",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="dynamic",
        primaryjoin="User.id == foreign(AuditLog.user_id)"
    )

    # Admin audit logs relationship
    admin_audit_logs = relationship(
        "AdminAuditLog",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="dynamic",
        primaryjoin="User.id == foreign(AdminAuditLog.user_id)"
    )

    # Error logs relationship
    error_logs = relationship(
        "ErrorLog",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="dynamic",
        primaryjoin="User.id == foreign(ErrorLog.user_id)"
    )

    admin_notifications = relationship(
        "AdminNotification",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="dynamic",
        primaryjoin="User.id == foreign(AdminNotification.user_id)"
    )

    # Environment relationships
    created_environments = relationship(
        "Environment",
        foreign_keys="[Environment.created_by]",
        back_populates="creator",
        lazy="dynamic",
        primaryjoin="User.id == foreign(Environment.created_by)"
    )

    revoked_environments = relationship(
        "Environment",
        foreign_keys="[Environment.revoked_by_id]",
        back_populates="revoker",
        lazy="dynamic",
        primaryjoin="User.id == foreign(Environment.revoked_by_id)"
    )

    # Assessment relationships
    created_assessments = relationship(
        "Assessment",
        foreign_keys="[Assessment.created_by]",
        back_populates="creator",
        lazy="dynamic",
        primaryjoin="User.id == foreign(Assessment.created_by)"
    )

    # Campaign relationships
    created_campaigns = relationship(
        "Campaign",
        foreign_keys="[Campaign.created_by]",
        back_populates="creator",
        lazy="dynamic",
        primaryjoin="User.id == foreign(Campaign.created_by)"
    )

    # TestCase relationships
    created_test_cases = relationship(
        "TestCase",
        foreign_keys="[TestCase.created_by]",
        back_populates="creator",
        lazy="dynamic",
        primaryjoin="User.id == foreign(TestCase.created_by)"
    )

    revoked_test_cases = relationship(
        "TestCase",
        foreign_keys="[TestCase.revoked_by_id]",
        back_populates="revoked_by",
        lazy="dynamic",
        primaryjoin="User.id == foreign(TestCase.revoked_by_id)"
    )

    # TestCaseTemplate relationships
    created_templates = relationship(
        "TestCaseTemplate",
        foreign_keys="TestCaseTemplate.created_by",
        back_populates="creator",
        lazy="dynamic",
    )

    # TestCaseDependency relationships
    created_dependencies = relationship(
        "TestCaseDependency",
        foreign_keys="TestCaseDependency.created_by",
        back_populates="creator",
        lazy="dynamic",
    )

    # TestSchedule relationships
    created_schedules = relationship(
        "TestSchedule",
        foreign_keys="TestSchedule.created_by",
        back_populates="creator",
        lazy="dynamic",
    )

    # Report relationships
    created_report_schedules = relationship(
        "ReportSchedule",
        foreign_keys="ReportSchedule.created_by",
        back_populates="creator",
        lazy="dynamic",
    )
    created_report_generations = relationship(
        "ReportGeneration",
        foreign_keys="ReportGeneration.created_by",
        back_populates="creator",
        lazy="dynamic",
    )

    # TestCaseHistory relationship
    test_case_changes = relationship(
        "TestCaseHistory",
        foreign_keys="TestCaseHistory.changed_by",
        back_populates="user",
        lazy="dynamic",
    )

    # TestExecution relationships
    executed_tests = relationship(
        "TestExecution",
        foreign_keys="[TestExecution.executed_by]",
        back_populates="executor",
        lazy="dynamic",
        primaryjoin="User.id == foreign(TestExecution.executed_by)"
    )

    # User activity relationship
    activities = relationship(
        "UserActivity",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="dynamic",
        primaryjoin="User.id == foreign(UserActivity.user_id)"
    )

    # Logging and Testing relationships
    log_entries = relationship("LogEntry", back_populates="user")
    test_scenarios = relationship("TestScenario", back_populates="creator")

    # Token management relationships
    blacklisted_tokens = relationship("TokenBlacklist", back_populates="user")
    refresh_tokens = relationship("RefreshToken", back_populates="user")

    # Workflow relationships
    workflows = relationship(
        "Workflow",
        foreign_keys="Workflow.created_by",
        back_populates="creator",
        lazy="dynamic",
    )

    # ML Model relationships
    ml_models = relationship(
        "MLModel",
        foreign_keys="MLModel.created_by",
        back_populates="creator",
        lazy="dynamic",
    )

    # Security tracking fields
    last_activity = Column(DateTime(timezone=True), nullable=True)
    last_password_change = Column(DateTime(timezone=True), nullable=True)
    password_history = Column(String(2048), nullable=True)  # Store last 5 password hashes

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """Generate a password hash."""
        return pwd_context.hash(password)

    def set_password(self, password: str) -> None:
        """Set user's password, converting it to a hash first."""
        self.hashed_password = self.get_password_hash(password)
        self.password_changed_at = datetime.now(tz=timezone.utc)
        # Reset security-related fields
        self.failed_login_attempts = 0
        self.account_locked = False
        self.account_locked_at = None
        self.password_reset_token = None
        self.password_reset_expires = None

    def check_password(self, password: str) -> bool:
        """Check if the provided password matches the user's password."""
        if self.account_locked:
            return False

        valid = self.verify_password(password, self.hashed_password)
        if valid:
            self.last_login = datetime.now(tz=timezone.utc)
            self.failed_login_attempts = 0
            self.last_failed_login = None
        else:
            self.failed_login_attempts += 1
            self.last_failed_login = datetime.now(tz=timezone.utc)

            # Lock account after 5 failed attempts
            if self.failed_login_attempts >= 5:
                self.account_locked = True
                self.account_locked_at = datetime.now(tz=timezone.utc)

        return valid

    # Two-factor authentication methods
    def generate_two_factor_secret(self) -> str:
        """Generate a new 2FA secret key."""
        self.two_factor_secret = pyotp.random_base32()
        return self.two_factor_secret

    def get_totp_uri(self) -> str:
        """Get the TOTP URI for QR code generation."""
        if not self.two_factor_secret:
            self.generate_two_factor_secret()
        return pyotp.totp.TOTP(self.two_factor_secret).provisioning_uri(
            name=self.email,
            issuer_name="RegrigorRigor",
        )

    def verify_totp(self, token: str) -> bool:
        """Verify a TOTP token."""
        if not self.two_factor_secret:
            return False
        totp = pyotp.TOTP(self.two_factor_secret)
        return totp.verify(token)

    def generate_backup_codes(self, count: int = 10) -> list[str]:
        """Generate backup codes for 2FA recovery."""
        import secrets
        import string

        # Generate random backup codes
        codes = []
        for _ in range(count):
            code = "".join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(10))
            codes.append(code)

        # Store hashed backup codes
        hashed_codes = [self.get_password_hash(code) for code in codes]
        self.backup_codes = ",".join(hashed_codes)

        return codes

    def verify_backup_code(self, code: str) -> bool:
        """Verify a backup code and remove it if valid."""
        if not self.backup_codes:
            return False

        hashed_codes = self.backup_codes.split(",")
        for i, hashed_code in enumerate(hashed_codes):
            if self.verify_password(code, hashed_code):
                # Remove the used backup code
                hashed_codes.pop(i)
                self.backup_codes = ",".join(hashed_codes)
                return True

        return False

    def enable_two_factor(self) -> None:
        """Enable two-factor authentication."""
        if not self.two_factor_secret:
            self.generate_two_factor_secret()
        self.two_factor_enabled = True

    def disable_two_factor(self) -> None:
        """Disable two-factor authentication."""
        self.two_factor_enabled = False
        self.two_factor_secret = None
        self.backup_codes = None

    def has_role(self, role: UserRole) -> bool:
        """Check if user has a specific role."""
        return self.role == role

    def is_admin(self) -> bool:
        """Check if user has admin role."""
        return self.role == UserRole.ADMIN

    def is_analyst(self) -> bool:
        """Check if user has analyst role."""
        return self.role == UserRole.ANALYST

    def can_access_resource(self, required_roles: list[UserRole]) -> bool:
        """Check if user has access to a resource based on their role."""
        return self.role in required_roles or self.is_admin()

    def verify_email(self) -> None:
        """Verify user's email address."""
        self.email_verified = True

    def update_profile(self, full_name: Optional[str] = None, bio: Optional[str] = None) -> None:
        """Update user profile information."""
        if full_name is not None:
            self.full_name = full_name
        if bio is not None:
            self.bio = bio

    def deactivate(self) -> None:
        """Deactivate user account."""
        self.is_active = False

    def activate(self) -> None:
        """Activate user account."""
        self.is_active = True

    def unlock_account(self) -> None:
        """Unlock a locked account."""
        self.account_locked = False
        self.account_locked_at = None
        self.failed_login_attempts = 0
        self.last_failed_login = None

    def set_password_reset_token(self, token: str, expires_in_hours: int = 24) -> None:
        """Set password reset token with expiration."""
        self.password_reset_token = token
        self.password_reset_expires = datetime.now(tz=timezone.utc) + timedelta(hours=expires_in_hours)

    def clear_password_reset_token(self) -> None:
        """Clear password reset token and expiration."""
        self.password_reset_token = None
        self.password_reset_expires = None

    def password_reset_token_valid(self) -> bool:
        """Check if password reset token is valid and not expired."""
        if not self.password_reset_token or not self.password_reset_expires:
            return False
        return datetime.now(tz=timezone.utc) < self.password_reset_expires

    def get_active_sessions(self) -> list[UserSession]:
        """Get all active sessions for the user."""
        return [session for session in self.sessions if session.is_valid]

    def terminate_other_sessions(self, current_session_id: str) -> None:
        """Terminate all sessions except the current one."""
        for session in self.sessions:
            if session.id != current_session_id:
                session.terminate()

    def generate_totp_secret(self):
        """Generate a new TOTP secret for the user."""
        if self.two_factor_enabled:
            raise ValueError("Two-factor authentication is already enabled")
        
        # Generate a random secret
        self.two_factor_secret = pyotp.random_base32()
        
        # Generate QR code
        totp = pyotp.TOTP(self.two_factor_secret)
        provisioning_uri = totp.provisioning_uri(
            name=self.email,
            issuer_name="RegrigorRigor"
        )
        
        # Create QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)
        
        # Create image and convert to base64
        img = qr.make_image(fill_color="black", back_color="white")
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_str = base64.b64encode(img_buffer.getvalue()).decode()
        
        return {
            'secret': self.two_factor_secret,
            'qr_code': f'data:image/png;base64,{img_str}'
        }

    def regenerate_backup_codes(self):
        """Regenerate backup codes for two-factor authentication."""
        if not self.two_factor_enabled:
            raise ValueError("Two-factor authentication is not enabled")
        
        # Generate new backup codes
        backup_codes = []
        for _ in range(10):  # Generate 10 backup codes
            code = secrets.token_hex(4)  # 8 characters
            hashed_code = pwd_context.hash(code)
            backup_codes.append({'code': code, 'hash': hashed_code})
        
        # Store hashed backup codes
        self.backup_codes = json.dumps([code['hash'] for code in backup_codes])
        
        # Return plain text codes to show to user
        return [code['code'] for code in backup_codes]