"""User activity tracking model."""
from datetime import datetime
from sqlalchemy import Column, String, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship
from uuid import uuid4

from api.database import Base
from api.utils.logging_config import get_logger

logger = get_logger("api.models.activity")

class UserActivity(Base):
    """Model for tracking user activity and important events."""
    
    __tablename__ = "user_activities"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    activity_type = Column(String, nullable=False)  # login, logout, settings_change, etc.
    description = Column(String)
    activity_metadata = Column(JSON)  # Additional context about the activity
    ip_address = Column(String)
    user_agent = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="activities")

    @classmethod
    def log_activity(cls, db_session, user_id: str, activity_type: str, description: str = None,
                    metadata: dict = None, ip_address: str = None, user_agent: str = None):
        """Log a user activity.
        
        Args:
            db_session: SQLAlchemy session
            user_id: ID of the user performing the activity
            activity_type: Type of activity (e.g., login, logout, settings_change)
            description: Human-readable description of the activity
            metadata: Additional context as a dictionary
            ip_address: IP address of the user
            user_agent: User agent string
        """
        try:
            activity = cls(
                user_id=user_id,
                activity_type=activity_type,
                description=description,
                activity_metadata=metadata,
                ip_address=ip_address,
                user_agent=user_agent
            )
            db_session.add(activity)
            db_session.commit()

            logger.info(
                f"User activity logged: {activity_type}",
                extra={
                    "user_id": user_id,
                    "activity_type": activity_type,
                    "description": description,
                    "metadata": metadata,
                    "ip_address": ip_address
                }
            )
        except Exception as e:
            logger.error(
                "Failed to log user activity",
                extra={
                    "error": str(e),
                    "user_id": user_id,
                    "activity_type": activity_type
                },
                exc_info=True
            )
            db_session.rollback()
            raise

    def __repr__(self):
        """String representation of the activity."""
        return f"<UserActivity(id={self.id}, user_id={self.user_id}, type={self.activity_type})>" 