"""Campaign management API endpoints.

This module provides API endpoints for managing security testing campaigns,
including creating, retrieving, updating, and deleting campaigns.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from api.database import get_db
from api.models.base import CampaignDB
from api.models.schemas import CampaignCreate, Campaign
from api.models.user import User
from api.auth.dependencies import get_current_user, get_current_active_user
from api.utils.rate_limiter import standard_rate_limit
from api.utils.error_handler import log_error
from api.schemas.campaign import (
    CampaignTestCase, CampaignTestCaseCreate,
    CampaignTestCaseUpdate, CampaignList
)
from api.services.campaign import CampaignService

router = APIRouter(
    prefix="/campaigns",
    tags=["campaigns"],
    responses={404: {"description": "Not found"}},
)

# Custom error handler for FastAPI routes
def fastapi_error_handler(func):
    """Decorator to handle SQLAlchemy errors in FastAPI routes."""
    from functools import wraps
    
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except SQLAlchemyError as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Return a user-friendly error response
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="A database error occurred. Our team has been notified."
            )
        except Exception as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Re-raise the exception
            raise
    
    return wrapper

@router.post("/", response_model=Campaign, status_code=status.HTTP_201_CREATED)
def create_campaign(
    campaign: CampaignCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    return CampaignService.create_campaign(db, campaign, current_user.id)

@router.get("/", response_model=CampaignList)
def list_campaigns(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    status: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    campaigns, total = CampaignService.get_campaigns(
        db, skip, limit, status, search
    )
    return CampaignList(total=total, items=campaigns)

@router.get("/{campaign_id}", response_model=Campaign)
def get_campaign(
    campaign_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    campaign = CampaignService.get_campaign(db, campaign_id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    return campaign

@router.put("/{campaign_id}", response_model=Campaign)
def update_campaign(
    campaign_id: int,
    campaign_update: CampaignCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    return CampaignService.update_campaign(db, campaign_id, campaign_update)

@router.delete("/{campaign_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_campaign(
    campaign_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    CampaignService.delete_campaign(db, campaign_id)
    return None

# Test Case Management Routes
@router.post(
    "/{campaign_id}/test-cases",
    response_model=CampaignTestCase,
    status_code=status.HTTP_201_CREATED
)
def add_test_case(
    campaign_id: int,
    test_case: CampaignTestCaseCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    return CampaignService.add_test_case(db, campaign_id, test_case)

@router.put(
    "/{campaign_id}/test-cases/{test_case_id}",
    response_model=CampaignTestCase
)
def update_test_case(
    campaign_id: int,
    test_case_id: int,
    test_case_update: CampaignTestCaseUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    return CampaignService.update_test_case(
        db, campaign_id, test_case_id, test_case_update
    )

@router.delete(
    "/{campaign_id}/test-cases/{test_case_id}",
    status_code=status.HTTP_204_NO_CONTENT
)
def remove_test_case(
    campaign_id: int,
    test_case_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    CampaignService.remove_test_case(db, campaign_id, test_case_id)
    return None 