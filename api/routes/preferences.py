"""API endpoints for managing user preferences."""

from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from api.database import get_db
from api.models.user import User
from api.models.user_preferences import UserPreference
from api.auth.dependencies import get_current_user

router = APIRouter()

class PreferenceUpdate(BaseModel):
    """Schema for updating user preferences."""
    theme: Optional[str] = None
    notification_settings: Optional[dict] = None
    dashboard_layout: Optional[dict] = None
    timezone: Optional[str] = None
    language: Optional[str] = None
    email_notifications: Optional[bool] = None
    two_factor_enabled: Optional[bool] = None

class PreferenceResponse(BaseModel):
    """Schema for preference response data."""
    theme: str
    notification_settings: dict
    dashboard_layout: dict
    timezone: str
    language: str
    email_notifications: bool
    two_factor_enabled: bool

    class Config:
        from_attributes = True

@router.get("/", response_model=PreferenceResponse)
async def get_preferences(current_user: User = Depends(get_current_user)):
    """Get current user's preferences."""
    if not current_user.preferences:
        # Create default preferences if none exist
        current_user.preferences = UserPreference()
    return current_user.preferences

@router.put("/", response_model=PreferenceResponse)
async def update_preferences(
    preferences: PreferenceUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user preferences."""
    if not current_user.preferences:
        current_user.preferences = UserPreference()

    # Update only provided fields
    for field, value in preferences.dict(exclude_unset=True).items():
        setattr(current_user.preferences, field, value)

    try:
        db.commit()
        db.refresh(current_user.preferences)
        return current_user.preferences
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating preferences: {str(e)}"
        )
