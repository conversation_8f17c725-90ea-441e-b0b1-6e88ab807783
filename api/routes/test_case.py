"""
API routes for test case management.

This module defines the FastAPI routes for managing test cases.
"""
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from api.database import get_db
from api.auth.dependencies import get_current_user, has_permission
from api.models.schemas.test_case import (
    TestCase, TestCaseCreate, TestCaseUpdate, TestCaseFilter,
    TestCaseBulkCreate, TestCaseBulkResponse, TestCaseWithCampaigns,
    TestCaseDeprecate, TestCaseRevoke, TestCaseStats
)
from api.services import test_case as test_case_service


router = APIRouter(prefix="/test-cases", tags=["Test Cases"])


@router.get("/", response_model=List[TestCase])
async def get_test_cases(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    status: Optional[str] = Query(None, description="Filter by test case status"),
    type: Optional[str] = Query(None, description="Filter by test case type"),
    priority: Optional[str] = Query(None, description="Filter by test case priority"),
    complexity: Optional[str] = Query(None, description="Filter by test case complexity"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    mitre_techniques: Optional[List[str]] = Query(None, description="Filter by MITRE ATT&CK techniques"),
    created_by: Optional[int] = Query(None, description="Filter by creator ID"),
    search: Optional[str] = Query(None, description="Search term for name and description"),
    include_deleted: bool = Query(False, description="Include soft-deleted test cases"),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get a list of test cases with optional filtering.
    
    Regular users can only see their own test cases, while admins can see all test cases.
    """
    # Check permissions
    is_admin = current_user.get("role") == "admin"
    user_id = current_user.get("id")
    
    # Create filter parameters
    filter_params = TestCaseFilter(
        status=status,
        type=type,
        priority=priority,
        complexity=complexity,
        tags=tags,
        mitre_techniques=mitre_techniques,
        created_by=created_by,
        search=search
    )
    
    # Get test cases
    test_cases = test_case_service.get_test_cases(
        db=db,
        skip=skip,
        limit=limit,
        filter_params=filter_params,
        include_deleted=include_deleted and is_admin,  # Only admins can see deleted test cases
        user_id=user_id,
        is_admin=is_admin
    )
    
    return test_cases


@router.get("/stats", response_model=TestCaseStats)
async def get_test_case_stats(
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get statistics about test cases.
    
    Regular users can only see statistics for their own test cases, while admins can see statistics for all test cases.
    """
    # Check permissions
    is_admin = current_user.get("role") == "admin"
    user_id = current_user.get("id")
    
    # Get statistics
    stats = test_case_service.get_test_case_stats(
        db=db,
        user_id=user_id,
        is_admin=is_admin
    )
    
    return stats


@router.get("/{test_case_id}", response_model=TestCase)
async def get_test_case(
    test_case_id: int = Path(..., description="ID of the test case to retrieve"),
    include_deleted: bool = Query(False, description="Include soft-deleted test cases"),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get a test case by ID.
    
    Regular users can only see their own test cases, while admins can see all test cases.
    """
    # Check permissions
    is_admin = current_user.get("role") == "admin"
    user_id = current_user.get("id")
    
    # Get test case
    test_case = test_case_service.get_test_case_by_id(
        db=db,
        test_case_id=test_case_id,
        include_deleted=include_deleted and is_admin  # Only admins can see deleted test cases
    )
    
    if not test_case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {test_case_id} not found"
        )
    
    # Check if user has permission to view this test case
    if not is_admin and test_case.created_by != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this test case"
        )
    
    return test_case


@router.post("/", response_model=TestCase, status_code=status.HTTP_201_CREATED)
async def create_test_case(
    test_case_data: TestCaseCreate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(has_permission("create_test_case"))
):
    """
    Create a new test case.
    
    Requires the 'create_test_case' permission.
    """
    user_id = current_user.get("id")
    
    # Create test case
    test_case = test_case_service.create_test_case(
        db=db,
        test_case_data=test_case_data,
        user_id=user_id
    )
    
    return test_case


@router.post("/bulk", response_model=TestCaseBulkResponse, status_code=status.HTTP_201_CREATED)
async def bulk_create_test_cases(
    test_cases_data: TestCaseBulkCreate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(has_permission("create_test_case"))
):
    """
    Create multiple test cases in bulk.
    
    Requires the 'create_test_case' permission.
    """
    user_id = current_user.get("id")
    
    # Create test cases
    created_test_cases, failed_entries = test_case_service.bulk_create_test_cases(
        db=db,
        test_cases_data=test_cases_data.test_cases,
        user_id=user_id
    )
    
    return {
        "test_cases": created_test_cases,
        "total_created": len(created_test_cases),
        "failed_entries": failed_entries
    }


@router.put("/{test_case_id}", response_model=TestCase)
async def update_test_case(
    test_case_data: TestCaseUpdate,
    test_case_id: int = Path(..., description="ID of the test case to update"),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update an existing test case.
    
    Regular users can only update their own test cases, while admins can update any test case.
    """
    # Check permissions
    is_admin = current_user.get("role") == "admin"
    user_id = current_user.get("id")
    
    # Update test case
    updated_test_case = test_case_service.update_test_case(
        db=db,
        test_case_id=test_case_id,
        test_case_data=test_case_data,
        user_id=user_id,
        is_admin=is_admin
    )
    
    if not updated_test_case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {test_case_id} not found"
        )
    
    return updated_test_case


@router.delete("/{test_case_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_test_case(
    test_case_id: int = Path(..., description="ID of the test case to delete"),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Soft-delete a test case.
    
    Regular users can only delete their own test cases, while admins can delete any test case.
    """
    # Check permissions
    is_admin = current_user.get("role") == "admin"
    user_id = current_user.get("id")
    
    # Delete test case
    success = test_case_service.delete_test_case(
        db=db,
        test_case_id=test_case_id,
        user_id=user_id,
        is_admin=is_admin
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {test_case_id} not found"
        )


@router.post("/{test_case_id}/restore", response_model=TestCase)
async def restore_test_case(
    test_case_id: int = Path(..., description="ID of the test case to restore"),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(has_permission("admin"))
):
    """
    Restore a soft-deleted test case.
    
    Requires admin privileges.
    """
    user_id = current_user.get("id")
    is_admin = current_user.get("role") == "admin"
    
    # Restore test case
    restored_test_case = test_case_service.restore_test_case(
        db=db,
        test_case_id=test_case_id,
        user_id=user_id,
        is_admin=is_admin
    )
    
    if not restored_test_case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {test_case_id} not found or not deleted"
        )
    
    return restored_test_case


@router.post("/{test_case_id}/deprecate", response_model=TestCase)
async def deprecate_test_case(
    deprecate_data: TestCaseDeprecate,
    test_case_id: int = Path(..., description="ID of the test case to deprecate"),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Mark a test case as deprecated.
    
    Regular users can only deprecate their own test cases, while admins can deprecate any test case.
    """
    # Check permissions
    is_admin = current_user.get("role") == "admin"
    user_id = current_user.get("id")
    
    # Deprecate test case
    deprecated_test_case = test_case_service.deprecate_test_case(
        db=db,
        test_case_id=test_case_id,
        user_id=user_id,
        is_admin=is_admin
    )
    
    if not deprecated_test_case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {test_case_id} not found"
        )
    
    return deprecated_test_case


@router.post("/{test_case_id}/revoke", response_model=TestCase)
async def revoke_test_case(
    revoke_data: TestCaseRevoke,
    test_case_id: int = Path(..., description="ID of the test case to revoke"),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(has_permission("admin"))
):
    """
    Revoke a test case.
    
    Requires admin privileges.
    """
    user_id = current_user.get("id")
    is_admin = current_user.get("role") == "admin"
    
    # Revoke test case
    revoked_test_case = test_case_service.revoke_test_case(
        db=db,
        test_case_id=test_case_id,
        user_id=user_id,
        is_admin=is_admin
    )
    
    if not revoked_test_case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {test_case_id} not found"
        )
    
    return revoked_test_case 