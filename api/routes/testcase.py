"""Test case management API endpoints.

This module provides API endpoints for managing security test cases,
including creating, retrieving, updating, and deleting test cases.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Security, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from api.database import get_db
from api.models.base import TestCaseDB, CampaignDB
from api.models.schemas import TestCaseCreate, TestCase
from api.models.user import User
from api.auth.dependencies import get_current_user, get_current_active_user
from api.utils.rate_limiter import standard_rate_limit
from api.utils.error_handler import log_error

router = APIRouter(prefix="/api/v1/testcases", tags=["testcases"])

# Custom error handler for FastAPI routes
def fastapi_error_handler(func):
    """Decorator to handle SQLAlchemy errors in FastAPI routes."""
    from functools import wraps
    
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except SQLAlchemyError as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Return a user-friendly error response
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="A database error occurred. Our team has been notified."
            )
        except Exception as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Re-raise the exception
            raise
    
    return wrapper

@router.post("/", response_model=TestCase, status_code=status.HTTP_201_CREATED)
@fastapi_error_handler
async def create_testcase(
    testcase_data: TestCaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Create a new security test case.
    
    Args:
        testcase_data: The test case data to create
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The created test case
    """
    # Check if user has permission to create test cases
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to create test cases"
        )
    
    # Verify that the campaign exists
    campaign = db.query(CampaignDB).filter(
        CampaignDB.id == testcase_data.campaign_id,
        CampaignDB.not_deleted()
    ).first()
    
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {testcase_data.campaign_id} not found"
        )
    
    # Create the test case
    testcase = TestCaseDB(
        name=testcase_data.name,
        description=testcase_data.description,
        campaign_id=testcase_data.campaign_id,
        expected_result=testcase_data.expected_result,
        actual_result=testcase_data.actual_result,
        status=testcase_data.status
    )
    
    db.add(testcase)
    db.commit()
    db.refresh(testcase)
    
    return testcase

@router.get("/", response_model=List[TestCase])
@fastapi_error_handler
async def get_testcases(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    campaign_id: Optional[int] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Get all test cases with optional filtering.
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        campaign_id: Optional filter by campaign ID
        status: Optional filter by test case status
        db: Database session
        current_user: The authenticated user
        
    Returns:
        List of test cases
    """
    query = db.query(TestCaseDB).filter(TestCaseDB.not_deleted())
    
    # Apply campaign filter if provided
    if campaign_id:
        query = query.filter(TestCaseDB.campaign_id == campaign_id)
    
    # Apply status filter if provided
    if status:
        query = query.filter(TestCaseDB.status == status)
    
    # Apply pagination
    testcases = query.offset(skip).limit(limit).all()
    
    return testcases

@router.get("/{testcase_id}", response_model=TestCase)
@fastapi_error_handler
async def get_testcase(
    testcase_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Get a specific test case by ID.
    
    Args:
        testcase_id: The ID of the test case to retrieve
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The requested test case
    """
    testcase = db.query(TestCaseDB).filter(
        TestCaseDB.id == testcase_id,
        TestCaseDB.not_deleted()
    ).first()
    
    if not testcase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {testcase_id} not found"
        )
    
    return testcase

@router.put("/{testcase_id}", response_model=TestCase)
@fastapi_error_handler
async def update_testcase(
    testcase_id: int,
    testcase_data: TestCaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Update a specific test case.
    
    Args:
        testcase_id: The ID of the test case to update
        testcase_data: The updated test case data
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The updated test case
    """
    # Check if user has permission to update test cases
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update test cases"
        )
    
    testcase = db.query(TestCaseDB).filter(
        TestCaseDB.id == testcase_id,
        TestCaseDB.not_deleted()
    ).first()
    
    if not testcase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {testcase_id} not found"
        )
    
    # Verify that the campaign exists if it's being changed
    if testcase_data.campaign_id != testcase.campaign_id:
        campaign = db.query(CampaignDB).filter(
            CampaignDB.id == testcase_data.campaign_id,
            CampaignDB.not_deleted()
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Campaign with ID {testcase_data.campaign_id} not found"
            )
    
    # Update test case fields
    testcase.name = testcase_data.name
    testcase.description = testcase_data.description
    testcase.campaign_id = testcase_data.campaign_id
    testcase.expected_result = testcase_data.expected_result
    testcase.actual_result = testcase_data.actual_result
    testcase.status = testcase_data.status
    
    db.add(testcase)
    db.commit()
    db.refresh(testcase)
    
    return testcase

@router.delete("/{testcase_id}", status_code=status.HTTP_204_NO_CONTENT)
@fastapi_error_handler
async def delete_testcase(
    testcase_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Delete (soft-delete) a test case.
    
    Args:
        testcase_id: The ID of the test case to delete
        db: Database session
        current_user: The authenticated user
    """
    # Check if user has permission to delete test cases
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete test cases"
        )
    
    testcase = db.query(TestCaseDB).filter(
        TestCaseDB.id == testcase_id,
        TestCaseDB.not_deleted()
    ).first()
    
    if not testcase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {testcase_id} not found"
        )
    
    # Soft delete the test case
    testcase.soft_delete(db)
    
    return None

@router.post("/{testcase_id}/restore", response_model=TestCase)
@fastapi_error_handler
async def restore_testcase(
    testcase_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Restore a soft-deleted test case.
    
    Args:
        testcase_id: The ID of the test case to restore
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The restored test case
    """
    # Check if user has permission to restore test cases
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to restore test cases"
        )
    
    testcase = db.query(TestCaseDB).filter(
        TestCaseDB.id == testcase_id,
        TestCaseDB.deleted_time.isnot(None)
    ).first()
    
    if not testcase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Deleted test case with ID {testcase_id} not found"
        )
    
    # Restore the test case
    testcase.deleted_time = None
    
    db.add(testcase)
    db.commit()
    db.refresh(testcase)
    
    return testcase

@router.post("/{testcase_id}/execute", response_model=TestCase)
@fastapi_error_handler
async def execute_testcase(
    testcase_id: int,
    execution_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Record the execution of a test case.
    
    Args:
        testcase_id: The ID of the test case to execute
        execution_data: The execution data including actual_result and status
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The updated test case
    """
    # Check if user has permission to execute test cases
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to execute test cases"
        )
    
    testcase = db.query(TestCaseDB).filter(
        TestCaseDB.id == testcase_id,
        TestCaseDB.not_deleted()
    ).first()
    
    if not testcase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Test case with ID {testcase_id} not found"
        )
    
    # Update test case with execution results
    if "actual_result" in execution_data:
        testcase.actual_result = execution_data["actual_result"]
    
    if "status" in execution_data:
        valid_statuses = ["pending", "running", "passed", "failed"]
        if execution_data["status"] not in valid_statuses:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
            )
        testcase.status = execution_data["status"]
    
    db.add(testcase)
    db.commit()
    db.refresh(testcase)
    
    return testcase 