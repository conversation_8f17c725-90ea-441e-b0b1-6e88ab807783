"""Global error handlers for the API."""
from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError
import traceback
import uuid

from api.utils.logging_config import get_logger

logger = get_logger("api.errors")

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors from FastAPI/Pydantic."""
    error_id = str(uuid.uuid4())
    logger.warning(
        "Validation error",
        extra={
            "error_id": error_id,
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host,
            "errors": exc.errors(),
        }
    )
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "Invalid request data",
            "error_id": error_id,
            "detail": "The provided data is invalid. Please check your input and try again."
        }
    )

async def database_exception_handler(request: Request, exc: SQLAlchemyError):
    """Handle database errors."""
    error_id = str(uuid.uuid4())
    logger.error(
        "Database error",
        extra={
            "error_id": error_id,
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host,
            "error": str(exc),
            "traceback": traceback.format_exc()
        }
    )
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Database error",
            "error_id": error_id,
            "detail": "An unexpected database error occurred. Please try again later."
        }
    )

async def pydantic_validation_handler(request: Request, exc: ValidationError):
    """Handle Pydantic validation errors."""
    error_id = str(uuid.uuid4())
    logger.warning(
        "Pydantic validation error",
        extra={
            "error_id": error_id,
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host,
            "errors": exc.errors()
        }
    )
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "Data validation error",
            "error_id": error_id,
            "detail": "The provided data format is invalid. Please check your input and try again."
        }
    )

async def generic_exception_handler(request: Request, exc: Exception):
    """Handle any unhandled exceptions."""
    error_id = str(uuid.uuid4())
    logger.error(
        "Unhandled exception",
        extra={
            "error_id": error_id,
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host,
            "error": str(exc),
            "error_type": type(exc).__name__,
            "traceback": traceback.format_exc()
        }
    )
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Internal server error",
            "error_id": error_id,
            "detail": "An unexpected error occurred. Please try again later."
        }
    )

# Export exception handlers
exception_handlers = {
    RequestValidationError: validation_exception_handler,
    SQLAlchemyError: database_exception_handler,
    ValidationError: pydantic_validation_handler,
    Exception: generic_exception_handler
} 