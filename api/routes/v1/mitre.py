"""API endpoints for MITRE ATT&CK data management."""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from api.database import get_db
from api.services.mitre import technique_service
from api.models.schemas.mitre import TechniqueCreate, TechniqueResponse
from typing import List, Optional
from datetime import datetime
from api.utils.rate_limiter import standard_rate_limit, strict_rate_limit
from api.auth.dependencies import get_current_user
from api.models.user import User
from api.models.mitre import ScoreCategory

router = APIRouter(prefix="/api/v1/mitre", tags=["mitre"])

@router.get("/techniques/scores/categories")
async def get_score_categories(
    _: None = Depends(standard_rate_limit)  # Apply standard rate limiting
):
    """Get all available technique score categories."""
    return [category.value for category in ScoreCategory]

@router.get("/techniques/", response_model=List[TechniqueResponse])
async def get_techniques(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    include_deleted: bool = False,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)  # Apply standard rate limiting
):
    """Get all techniques with pagination."""
    return technique_service.get_all(
        db, 
        skip_deleted=not include_deleted,
        skip=skip,
        limit=limit
    )

@router.get("/techniques/{technique_id}", response_model=TechniqueResponse)
async def get_technique(
    technique_id: str,
    include_deleted: bool = False,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)  # Apply standard rate limiting
):
    """Get a specific technique by its MITRE ID."""
    technique = technique_service.get_by_technique_id(
        db, 
        technique_id,
        skip_deleted=not include_deleted
    )
    if not technique:
        raise HTTPException(status_code=404, detail="Technique not found")
    return technique

@router.post("/techniques/", response_model=TechniqueResponse)
async def create_technique(
    technique: TechniqueCreate,
    tactic_ids: List[int] = Query(None),
    group_ids: List[int] = Query(None),
    software_ids: List[int] = Query(None),
    mitigation_ids: List[int] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Create a new technique with optional relationships."""
    return technique_service.create_with_relationships(
        db,
        technique.dict(),
        tactic_ids=tactic_ids,
        group_ids=group_ids,
        software_ids=software_ids,
        mitigation_ids=mitigation_ids
    )

@router.put("/techniques/{technique_id}", response_model=TechniqueResponse)
async def update_technique(
    technique_id: str,
    technique_data: TechniqueCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Update a technique."""
    existing = technique_service.get_by_technique_id(db, technique_id)
    if not existing:
        raise HTTPException(status_code=404, detail="Technique not found")
    return technique_service.update(db, existing, technique_data.dict())

@router.delete("/techniques/{technique_id}")
async def delete_technique(
    technique_id: str, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Soft-delete a technique."""
    technique = technique_service.get_by_technique_id(db, technique_id)
    if not technique:
        raise HTTPException(status_code=404, detail="Technique not found")
    technique_service.delete(db, technique)
    return {"message": "Technique soft-deleted successfully"}

@router.post("/techniques/{technique_id}/restore", response_model=TechniqueResponse)
async def restore_technique(
    technique_id: str, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Restore a soft-deleted technique."""
    technique = technique_service.get_by_technique_id(
        db, 
        technique_id,
        skip_deleted=False
    )
    if not technique:
        raise HTTPException(status_code=404, detail="Technique not found")
    return technique_service.restore(db, technique)
