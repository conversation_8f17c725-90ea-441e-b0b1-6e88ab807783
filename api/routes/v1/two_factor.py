"""Two-factor authentication routes for the FastAPI application."""

import secrets
from fastapi import <PERSON><PERSON><PERSON>er, Depends, HTTPException, status
from sqlalchemy.orm import Session
import qrcode
import io
import base64
from typing import List

from api.database import get_db
from api.models.user import User
from api.models.schemas import (
    TwoFactorSetup,
    TwoFactorEnable,
    TwoFactorVerify,
    TwoFactorDisable,
    TwoFactorStatus
)
from api.auth.dependencies import get_current_user
from api.utils.rate_limiter import standard_rate_limit, strict_rate_limit

router = APIRouter(prefix="/two-factor", tags=["Two-Factor Authentication"])

@router.get("/status", response_model=TwoFactorStatus)
async def get_two_factor_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)  # Apply standard rate limiting
):
    """Get the current 2FA status for the authenticated user."""
    backup_codes_count = 0
    if current_user.backup_codes:
        backup_codes_count = len(current_user.backup_codes.split(','))
    
    return {
        "enabled": current_user.two_factor_enabled,
        "backup_codes_remaining": backup_codes_count
    }

@router.post("/setup", response_model=TwoFactorSetup)
async def setup_two_factor(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Set up two-factor authentication for the authenticated user."""
    # Generate a new secret key
    secret = current_user.generate_two_factor_secret()
    
    # Generate QR code URI
    qr_code_uri = current_user.get_totp_uri()
    
    # Generate backup codes
    backup_codes = current_user.generate_backup_codes()
    
    # Save changes to the database
    db.commit()
    
    return {
        "secret": secret,
        "qr_code_uri": qr_code_uri,
        "backup_codes": backup_codes
    }

@router.post("/enable")
async def enable_two_factor(
    data: TwoFactorEnable,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Enable two-factor authentication after setup."""
    # Verify the provided token
    if not current_user.verify_totp(data.token):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification code"
        )
    
    # Enable 2FA
    current_user.enable_two_factor()
    db.commit()
    
    return {"message": "Two-factor authentication enabled successfully"}

@router.post("/disable")
async def disable_two_factor(
    data: TwoFactorDisable,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Disable two-factor authentication."""
    # Verify the user's password
    if not current_user.check_password(data.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid password"
        )
    
    # Disable 2FA
    current_user.disable_two_factor()
    db.commit()
    
    return {"message": "Two-factor authentication disabled successfully"}

@router.post("/regenerate-backup-codes", response_model=List[str])
async def regenerate_backup_codes(
    data: TwoFactorDisable,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Regenerate backup codes for 2FA recovery."""
    # Verify the user's password
    if not current_user.check_password(data.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid password"
        )
    
    # Generate new backup codes
    backup_codes = current_user.generate_backup_codes()
    db.commit()
    
    return backup_codes 