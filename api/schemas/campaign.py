from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field

class CampaignBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    status: str = Field(default="draft", pattern="^(draft|active|completed|archived)$")
    start_date: datetime
    end_date: datetime

class CampaignCreate(CampaignBase):
    pass

class CampaignUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    status: Optional[str] = Field(None, pattern="^(draft|active|completed|archived)$")
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

class CampaignTestCaseBase(BaseModel):
    test_case_id: int
    status: str = Field(default="pending", pattern="^(pending|in_progress|completed|failed)$")
    assigned_to: Optional[int] = None
    notes: Optional[str] = None

class CampaignTestCaseCreate(CampaignTestCaseBase):
    pass

class CampaignTestCaseUpdate(BaseModel):
    status: Optional[str] = Field(None, pattern="^(pending|in_progress|completed|failed)$")
    assigned_to: Optional[int] = None
    notes: Optional[str] = None

class CampaignTestCase(CampaignTestCaseBase):
    id: int
    campaign_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class Campaign(CampaignBase):
    id: int
    created_by: int
    created_at: datetime
    updated_at: datetime
    is_deleted: bool = False
    test_cases: List[CampaignTestCase] = []

    class Config:
        from_attributes = True

class CampaignList(BaseModel):
    total: int
    items: List[Campaign] 