"""
Service layer for campaign operations.

This module contains the business logic for managing campaigns.
It acts as an intermediary between the API routes and the database models.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from fastapi import HTTPException, status

from api.models.campaign import CampaignDB, CampaignTestCase
from api.models.schemas.campaign import (
    CampaignCreate, CampaignUpdate, 
    CampaignSummary, TestCaseAssignment,
    CampaignTestCaseUpdate
)
from api.schemas.campaign import CampaignTestCaseCreate


def get_campaigns(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    status: Optional[str] = None,
    search: Optional[str] = None,
    created_by: Optional[int] = None,
    include_deleted: bool = False
) -> List[CampaignDB]:
    """
    Get a list of campaigns with optional filtering.
    
    Args:
        db: Database session
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        status: Filter by campaign status
        search: Search term for name or description
        created_by: Filter by creator user ID
        include_deleted: Whether to include soft-deleted campaigns
        
    Returns:
        List of campaign objects
    """
    query = db.query(CampaignDB)
    
    # Apply filters
    if not include_deleted:
        query = query.filter(CampaignDB.deleted_at.is_(None))
    
    if status:
        query = query.filter(CampaignDB.status == status)
    
    if created_by:
        query = query.filter(CampaignDB.created_by == created_by)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                CampaignDB.name.ilike(search_term),
                CampaignDB.description.ilike(search_term)
            )
        )
    
    # Apply pagination
    query = query.order_by(CampaignDB.created_at.desc()).offset(skip).limit(limit)
    
    return query.all()


def get_campaign_by_id(db: Session, campaign_id: int, include_deleted: bool = False) -> Optional[CampaignDB]:
    """
    Get a campaign by its ID.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign to retrieve
        include_deleted: Whether to include soft-deleted campaigns
        
    Returns:
        Campaign object if found, None otherwise
    """
    query = db.query(CampaignDB).filter(CampaignDB.id == campaign_id)
    
    if not include_deleted:
        query = query.filter(CampaignDB.deleted_at.is_(None))
    
    return query.first()


def create_campaign(db: Session, campaign_data: CampaignCreate, user_id: int) -> CampaignDB:
    """
    Create a new campaign.
    
    Args:
        db: Database session
        campaign_data: Campaign data
        user_id: ID of the user creating the campaign
        
    Returns:
        Created campaign object
    """
    campaign = CampaignDB(
        name=campaign_data.name,
        description=campaign_data.description,
        status=campaign_data.status.value,
        start_date=campaign_data.start_date,
        end_date=campaign_data.end_date,
        created_by=user_id
    )
    
    db.add(campaign)
    db.commit()
    db.refresh(campaign)
    
    return campaign


def update_campaign(
    db: Session, 
    campaign_id: int, 
    campaign_data: CampaignUpdate
) -> Optional[CampaignDB]:
    """
    Update an existing campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign to update
        campaign_data: Updated campaign data
        
    Returns:
        Updated campaign object if found, None otherwise
    """
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return None
    
    # Update fields if provided
    if campaign_data.name is not None:
        campaign.name = campaign_data.name
    
    if campaign_data.description is not None:
        campaign.description = campaign_data.description
    
    if campaign_data.status is not None:
        campaign.status = campaign_data.status.value
    
    if campaign_data.start_date is not None:
        campaign.start_date = campaign_data.start_date
    
    if campaign_data.end_date is not None:
        campaign.end_date = campaign_data.end_date
    
    campaign.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(campaign)
    
    return campaign


def delete_campaign(db: Session, campaign_id: int) -> bool:
    """
    Soft-delete a campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign to delete
        
    Returns:
        True if the campaign was deleted, False otherwise
    """
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return False
    
    campaign.soft_delete(db)
    
    return True


def restore_campaign(db: Session, campaign_id: int) -> bool:
    """
    Restore a soft-deleted campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign to restore
        
    Returns:
        True if the campaign was restored, False otherwise
    """
    campaign = get_campaign_by_id(db, campaign_id, include_deleted=True)
    
    if not campaign or not campaign.deleted_at:
        return False
    
    campaign.deleted_at = None
    db.commit()
    
    return True


def get_campaign_test_cases(db: Session, campaign_id: int, skip: int = 0, limit: int = 100) -> List[Any]:
    """
    Get test cases associated with a campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        
    Returns:
        List of campaign test case objects
    """
    return (
        db.query(CampaignTestCase)
        .filter(CampaignTestCase.campaign_id == campaign_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def assign_test_cases(db: Session, campaign_id: int, assignment: TestCaseAssignment) -> bool:
    """
    Assign test cases to a campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign
        assignment: Test case assignment data
        
    Returns:
        True if the assignment was successful, False otherwise
    """
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return False
    
    for test_case_id in assignment.test_case_ids:
        campaign_test_case = CampaignTestCase(
            campaign_id=campaign_id,
            test_case_id=test_case_id,
            status=assignment.status.value if assignment.status else None,
            assigned_to=assignment.assigned_to
        )
        db.add(campaign_test_case)
    
    try:
        db.commit()
        return True
    except Exception:
        db.rollback()
        return False


def remove_test_case(db: Session, campaign_id: int, test_case_id: int) -> bool:
    """
    Remove a test case from a campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign
        test_case_id: ID of the test case to remove
        
    Returns:
        True if the test case was removed, False otherwise
    """
    campaign_test_case = (
        db.query(CampaignTestCase)
        .filter(
            CampaignTestCase.campaign_id == campaign_id,
            CampaignTestCase.test_case_id == test_case_id
        )
        .first()
    )
    
    if not campaign_test_case:
        return False
    
    db.delete(campaign_test_case)
    db.commit()
    
    return True


def get_campaign_summary(db: Session, campaign_id: int) -> Optional[CampaignSummary]:
    """
    Get a summary of a campaign's test cases.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign
        
    Returns:
        Campaign summary object if found, None otherwise
    """
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return None
    
    # Get test case counts by status
    status_counts = (
        db.query(
            CampaignTestCase.status,
            func.count(CampaignTestCase.id).label('count')
        )
        .filter(CampaignTestCase.campaign_id == campaign_id)
        .group_by(CampaignTestCase.status)
        .all()
    )
    
    # Convert to dictionary
    status_dict = {status: count for status, count in status_counts}
    
    return CampaignSummary(
        campaign_id=campaign_id,
        name=campaign.name,
        status=campaign.status,
        start_date=campaign.start_date,
        end_date=campaign.end_date,
        total_test_cases=sum(status_dict.values()),
        status_counts=status_dict
    )


def get_campaigns_by_assessment(
    db: Session, 
    assessment_id: int, 
    skip: int = 0, 
    limit: int = 100,
    include_deleted: bool = False
) -> List[CampaignDB]:
    """
    Get campaigns associated with an assessment.
    
    Args:
        db: Database session
        assessment_id: ID of the assessment
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        include_deleted: Whether to include soft-deleted campaigns
        
    Returns:
        List of campaign objects
    """
    query = (
        db.query(CampaignDB)
        .filter(CampaignDB.assessment_id == assessment_id)
    )
    
    if not include_deleted:
        query = query.filter(CampaignDB.deleted_at.is_(None))
    
    return query.offset(skip).limit(limit).all()


class CampaignService:
    """Service class for campaign operations."""
    
    @staticmethod
    def create_campaign(db: Session, campaign: CampaignCreate, user_id: int) -> CampaignDB:
        """Create a new campaign."""
        db_campaign = CampaignDB(
            name=campaign.name,
            description=campaign.description,
            status=campaign.status.value,
            start_date=campaign.start_date,
            end_date=campaign.end_date,
            created_by=user_id
        )
        db.add(db_campaign)
        db.commit()
        db.refresh(db_campaign)
        return db_campaign

    @staticmethod
    def get_campaign(db: Session, campaign_id: int) -> Optional[CampaignDB]:
        """Get a campaign by ID."""
        return db.query(CampaignDB).filter(
            CampaignDB.id == campaign_id,
            CampaignDB.deleted_at.is_(None)
        ).first()

    @staticmethod
    def get_campaigns(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        search: Optional[str] = None
    ) -> tuple[List[CampaignDB], int]:
        """Get a list of campaigns with optional filtering."""
        query = db.query(CampaignDB).filter(CampaignDB.deleted_at.is_(None))
        
        if status:
            query = query.filter(CampaignDB.status == status)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    CampaignDB.name.ilike(search_term),
                    CampaignDB.description.ilike(search_term)
                )
            )
        
        total = query.count()
        campaigns = query.order_by(CampaignDB.created_at.desc()).offset(skip).limit(limit).all()
        
        return campaigns, total

    @staticmethod
    def update_campaign(db: Session, campaign_id: int, campaign_update: CampaignUpdate) -> CampaignDB:
        """Update a campaign."""
        campaign = CampaignService.get_campaign(db, campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        for field, value in campaign_update.dict(exclude_unset=True).items():
            if hasattr(campaign, field):
                setattr(campaign, field, value.value if hasattr(value, 'value') else value)
        
        db.commit()
        db.refresh(campaign)
        return campaign

    @staticmethod
    def delete_campaign(db: Session, campaign_id: int) -> bool:
        """Delete a campaign."""
        campaign = CampaignService.get_campaign(db, campaign_id)
        if not campaign:
            return False
        
        campaign.soft_delete(db)
        return True

    @staticmethod
    def add_test_case(
        db: Session,
        campaign_id: int,
        test_case: CampaignTestCaseCreate
    ) -> CampaignTestCase:
        """Add a test case to a campaign."""
        campaign = CampaignService.get_campaign(db, campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        db_test_case = CampaignTestCase(
            campaign_id=campaign_id,
            test_case_id=test_case.test_case_id,
            status=test_case.status.value,
            assigned_to=test_case.assigned_to
        )
        db.add(db_test_case)
        db.commit()
        db.refresh(db_test_case)
        return db_test_case

    @staticmethod
    def update_test_case(
        db: Session,
        campaign_id: int,
        test_case_id: int,
        test_case_update: CampaignTestCaseUpdate
    ) -> CampaignTestCase:
        """Update a test case in a campaign."""
        test_case = db.query(CampaignTestCase).filter(
            CampaignTestCase.campaign_id == campaign_id,
            CampaignTestCase.test_case_id == test_case_id
        ).first()
        
        if not test_case:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Test case not found in campaign"
            )
        
        for field, value in test_case_update.dict(exclude_unset=True).items():
            if hasattr(test_case, field):
                setattr(test_case, field, value.value if hasattr(value, 'value') else value)
        
        db.commit()
        db.refresh(test_case)
        return test_case

    @staticmethod
    def remove_test_case(db: Session, campaign_id: int, test_case_id: int) -> bool:
        """Remove a test case from a campaign."""
        test_case = db.query(CampaignTestCase).filter(
            CampaignTestCase.campaign_id == campaign_id,
            CampaignTestCase.test_case_id == test_case_id
        ).first()
        
        if not test_case:
            return False
        
        db.delete(test_case)
        db.commit()
        return True 