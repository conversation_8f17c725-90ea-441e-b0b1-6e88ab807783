"""
Service layer for test case management.

This module provides functions for managing test cases, including CRUD operations
and business logic.
"""
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

from fastapi import HTTPException
from sqlalchemy import or_, and_, func
from sqlalchemy.orm import Session

from api.models.database.test_case import TestCaseDB
from api.models.schemas.test_case import (
    TestCaseCreate, TestCaseUpdate, TestCaseFilter,
    TestCaseStatus, TestCaseType, TestCasePriority, TestCaseComplexity
)


def get_test_cases(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    filter_params: Optional[TestCaseFilter] = None,
    include_deleted: bool = False,
    user_id: Optional[int] = None,
    is_admin: bool = False
) -> List[TestCaseDB]:
    """
    Get a list of test cases with optional filtering.
    
    Args:
        db: Database session
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        filter_params: Filter parameters
        include_deleted: Whether to include soft-deleted test cases
        user_id: ID of the current user
        is_admin: Whether the current user is an admin
        
    Returns:
        List of test cases
    """
    query = db.query(TestCaseDB)
    
    # Apply filters
    if filter_params:
        if filter_params.status:
            query = query.filter(TestCaseDB.status == filter_params.status)
        
        if filter_params.type:
            query = query.filter(TestCaseDB.type == filter_params.type)
        
        if filter_params.priority:
            query = query.filter(TestCaseDB.priority == filter_params.priority)
        
        if filter_params.complexity:
            query = query.filter(TestCaseDB.complexity == filter_params.complexity)
        
        if filter_params.tags:
            for tag in filter_params.tags:
                query = query.filter(TestCaseDB.tags.contains([tag]))
        
        if filter_params.mitre_techniques:
            for technique in filter_params.mitre_techniques:
                query = query.filter(TestCaseDB.mitre_techniques.contains([technique]))
        
        if filter_params.created_by:
            query = query.filter(TestCaseDB.created_by == filter_params.created_by)
        
        if filter_params.search:
            search_term = f"%{filter_params.search}%"
            query = query.filter(
                or_(
                    TestCaseDB.name.ilike(search_term),
                    TestCaseDB.description.ilike(search_term)
                )
            )
    
    # Filter by user permissions
    if not is_admin and user_id:
        query = query.filter(TestCaseDB.created_by == user_id)
    
    # Filter deleted records
    if not include_deleted:
        query = query.filter(TestCaseDB.not_deleted())
    
    # Apply pagination
    return query.order_by(TestCaseDB.id).offset(skip).limit(limit).all()


def get_test_case_by_id(
    db: Session,
    test_case_id: int,
    include_deleted: bool = False
) -> Optional[TestCaseDB]:
    """
    Get a test case by ID.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to retrieve
        include_deleted: Whether to include soft-deleted test cases
        
    Returns:
        Test case if found, None otherwise
    """
    query = db.query(TestCaseDB).filter(TestCaseDB.id == test_case_id)
    
    if not include_deleted:
        query = query.filter(TestCaseDB.not_deleted())
    
    return query.first()


def create_test_case(
    db: Session,
    test_case_data: TestCaseCreate,
    user_id: int
) -> TestCaseDB:
    """
    Create a new test case.
    
    Args:
        db: Database session
        test_case_data: Data for the new test case
        user_id: ID of the user creating the test case
        
    Returns:
        Created test case
    """
    # Convert steps, tags, and mitre_techniques to JSON
    steps = test_case_data.steps if test_case_data.steps else []
    tags = test_case_data.tags if test_case_data.tags else []
    mitre_techniques = test_case_data.mitre_techniques if test_case_data.mitre_techniques else []
    
    # Create the test case
    db_test_case = TestCaseDB(
        name=test_case_data.name,
        description=test_case_data.description,
        type=test_case_data.type,
        status=test_case_data.status,
        priority=test_case_data.priority,
        complexity=test_case_data.complexity,
        prerequisites=test_case_data.prerequisites,
        steps=steps,
        expected_result=test_case_data.expected_result,
        tags=tags,
        mitre_techniques=mitre_techniques,
        created_by=user_id,
        version="1.0.0"
    )
    
    db.add(db_test_case)
    db.commit()
    db.refresh(db_test_case)
    
    return db_test_case


def bulk_create_test_cases(
    db: Session,
    test_cases_data: List[TestCaseCreate],
    user_id: int
) -> Tuple[List[TestCaseDB], List[Dict[str, Any]]]:
    """
    Create multiple test cases in bulk.
    
    Args:
        db: Database session
        test_cases_data: List of test case data
        user_id: ID of the user creating the test cases
        
    Returns:
        Tuple of (created test cases, failed entries)
    """
    created_test_cases = []
    failed_entries = []
    
    for index, test_case_data in enumerate(test_cases_data):
        try:
            test_case = create_test_case(db, test_case_data, user_id)
            created_test_cases.append(test_case)
        except Exception as e:
            failed_entries.append({
                "index": index,
                "data": test_case_data.dict(),
                "error": str(e)
            })
    
    return created_test_cases, failed_entries


def update_test_case(
    db: Session,
    test_case_id: int,
    test_case_data: TestCaseUpdate,
    user_id: int,
    is_admin: bool = False
) -> Optional[TestCaseDB]:
    """
    Update a test case.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to update
        test_case_data: Updated test case data
        user_id: ID of the user updating the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        Updated test case if found and user has permission, None otherwise
    """
    test_case = get_test_case_by_id(db, test_case_id)
    
    if not test_case:
        return None
    
    # Check permissions
    if not is_admin and test_case.created_by != user_id:
        raise HTTPException(
            status_code=403,
            detail="You don't have permission to update this test case"
        )
    
    # Update fields if provided
    if test_case_data.name is not None:
        test_case.name = test_case_data.name
    
    if test_case_data.description is not None:
        test_case.description = test_case_data.description
    
    if test_case_data.type is not None:
        test_case.type = test_case_data.type
    
    if test_case_data.status is not None:
        test_case.status = test_case_data.status
    
    if test_case_data.priority is not None:
        test_case.priority = test_case_data.priority
    
    if test_case_data.complexity is not None:
        test_case.complexity = test_case_data.complexity
    
    if test_case_data.prerequisites is not None:
        test_case.prerequisites = test_case_data.prerequisites
    
    if test_case_data.steps is not None:
        test_case.steps = test_case_data.steps
    
    if test_case_data.expected_result is not None:
        test_case.expected_result = test_case_data.expected_result
    
    if test_case_data.tags is not None:
        test_case.tags = test_case_data.tags
    
    if test_case_data.mitre_techniques is not None:
        test_case.mitre_techniques = test_case_data.mitre_techniques
    
    test_case.updated_at = datetime.utcnow()
    test_case.updated_by = user_id
    
    db.commit()
    db.refresh(test_case)
    
    return test_case


def delete_test_case(
    db: Session,
    test_case_id: int,
    user_id: int,
    is_admin: bool = False
) -> bool:
    """
    Soft-delete a test case.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to delete
        user_id: ID of the user deleting the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        True if the test case was deleted, False otherwise
    """
    test_case = get_test_case_by_id(db, test_case_id)
    
    if not test_case:
        return False
    
    # Check permissions
    if not is_admin and test_case.created_by != user_id:
        raise HTTPException(
            status_code=403,
            detail="You don't have permission to delete this test case"
        )
    
    test_case.soft_delete(db)
    
    return True


def restore_test_case(
    db: Session,
    test_case_id: int,
    user_id: int,
    is_admin: bool = False
) -> Optional[TestCaseDB]:
    """
    Restore a soft-deleted test case.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to restore
        user_id: ID of the user restoring the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        Restored test case if found and user has permission, None otherwise
    """
    test_case = get_test_case_by_id(db, test_case_id, include_deleted=True)
    
    if not test_case or not test_case.deleted_at:
        return None
    
    # Check permissions
    if not is_admin and test_case.created_by != user_id:
        raise HTTPException(
            status_code=403,
            detail="You don't have permission to restore this test case"
        )
    
    test_case.deleted_at = None
    test_case.updated_at = datetime.utcnow()
    test_case.updated_by = user_id
    
    db.commit()
    db.refresh(test_case)
    
    return test_case


def deprecate_test_case(
    db: Session,
    test_case_id: int,
    user_id: int,
    is_admin: bool = False
) -> Optional[TestCaseDB]:
    """
    Mark a test case as deprecated.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to deprecate
        user_id: ID of the user deprecating the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        Deprecated test case if found and user has permission, None otherwise
    """
    test_case = get_test_case_by_id(db, test_case_id)
    
    if not test_case:
        return None
    
    # Check permissions
    if not is_admin and test_case.created_by != user_id:
        raise HTTPException(
            status_code=403,
            detail="You don't have permission to deprecate this test case"
        )
    
    test_case.deprecated = True
    test_case.updated_at = datetime.utcnow()
    test_case.updated_by = user_id
    
    db.commit()
    db.refresh(test_case)
    
    return test_case


def revoke_test_case(
    db: Session,
    test_case_id: int,
    user_id: int,
    is_admin: bool = False
) -> Optional[TestCaseDB]:
    """
    Mark a test case as revoked.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to revoke
        user_id: ID of the user revoking the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        Revoked test case if found and user has permission, None otherwise
    """
    test_case = get_test_case_by_id(db, test_case_id)
    
    if not test_case:
        return None
    
    # Check permissions
    if not is_admin and test_case.created_by != user_id:
        raise HTTPException(
            status_code=403,
            detail="You don't have permission to revoke this test case"
        )
    
    test_case.revoked = True
    test_case.updated_at = datetime.utcnow()
    test_case.updated_by = user_id
    
    db.commit()
    db.refresh(test_case)
    
    return test_case


def get_test_case_stats(
    db: Session,
    user_id: Optional[int] = None,
    is_admin: bool = False
) -> Dict[str, Any]:
    """
    Get statistics about test cases.
    
    Args:
        db: Database session
        user_id: ID of the current user
        is_admin: Whether the current user is an admin
        
    Returns:
        Dictionary containing test case statistics
    """
    query = db.query(TestCaseDB).filter(TestCaseDB.not_deleted())
    
    # Filter by user permissions
    if not is_admin and user_id:
        query = query.filter(TestCaseDB.created_by == user_id)
    
    # Get total count
    total_count = query.count()
    
    # Get counts by status
    status_counts = (
        query.with_entities(
            TestCaseDB.status,
            func.count(TestCaseDB.id).label('count')
        )
        .group_by(TestCaseDB.status)
        .all()
    )
    
    # Get counts by type
    type_counts = (
        query.with_entities(
            TestCaseDB.type,
            func.count(TestCaseDB.id).label('count')
        )
        .group_by(TestCaseDB.type)
        .all()
    )
    
    # Get counts by priority
    priority_counts = (
        query.with_entities(
            TestCaseDB.priority,
            func.count(TestCaseDB.id).label('count')
        )
        .group_by(TestCaseDB.priority)
        .all()
    )
    
    # Get counts by complexity
    complexity_counts = (
        query.with_entities(
            TestCaseDB.complexity,
            func.count(TestCaseDB.id).label('count')
        )
        .group_by(TestCaseDB.complexity)
        .all()
    )
    
    return {
        "total": total_count,
        "by_status": {status: count for status, count in status_counts},
        "by_type": {type_: count for type_, count in type_counts},
        "by_priority": {priority: count for priority, count in priority_counts},
        "by_complexity": {complexity: count for complexity, count in complexity_counts}
    } 