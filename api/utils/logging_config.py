"""Logging configuration for the application."""
import logging
import logging.handlers
import json
from datetime import datetime
from typing import Any, Dict
import os
from pathlib import Path

# Create logs directory if it doesn't exist
LOGS_DIR = Path("logs")
LOGS_DIR.mkdir(exist_ok=True)

class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""

    def format(self, record: logging.LogRecord) -> str:
        """Format the log record as JSON."""
        log_data: Dict[str, Any] = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "message": record.getMessage(),
        }

        # Add extra fields if they exist
        if hasattr(record, "extra_data"):
            log_data.update(record.extra_data)
        
        # Process any extra fields added via extra keyword argument
        extra_attrs = [attr for attr in dir(record) if not attr.startswith('_') 
                      and attr not in ('args', 'asctime', 'created', 'exc_info', 'exc_text', 
                                      'filename', 'funcName', 'getMessage', 'levelname', 'levelno', 
                                      'lineno', 'message', 'module', 'msecs', 'msg', 
                                      'name', 'pathname', 'process', 'processName', 
                                      'relativeCreated', 'stack_info', 'thread', 'threadName',
                                      'extra_data')]
        
        for attr in extra_attrs:
            if attr != 'extra_data':  # Already processed
                log_data[attr] = getattr(record, attr)

        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_data)

def setup_logging(app_name: str = "regrigor") -> None:
    """Set up logging configuration.
    
    Args:
        app_name: Name of the application for log file naming
    """
    # Create handlers
    console_handler = logging.StreamHandler()
    file_handler = logging.handlers.RotatingFileHandler(
        filename=LOGS_DIR / f"{app_name}.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    error_handler = logging.handlers.RotatingFileHandler(
        filename=LOGS_DIR / f"{app_name}_error.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )

    # Set formatters
    json_formatter = JSONFormatter()
    console_handler.setFormatter(json_formatter)
    file_handler.setFormatter(json_formatter)
    error_handler.setFormatter(json_formatter)

    # Set levels
    console_handler.setLevel(logging.INFO)
    file_handler.setLevel(logging.DEBUG)
    error_handler.setLevel(logging.ERROR)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)

    # Create specific loggers
    loggers = {
        "auth": logging.getLogger("auth"),
        "db": logging.getLogger("db"),
        "api": logging.getLogger("api"),
        "ui": logging.getLogger("ui")
    }

    # Configure specific loggers
    for logger in loggers.values():
        logger.setLevel(logging.DEBUG)
        logger.propagate = True  # Allow propagation to root logger

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance.
    
    Args:
        name: Name of the logger
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)
