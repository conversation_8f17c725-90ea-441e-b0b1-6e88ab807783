"""Flask application module for user management and authentication."""
import os
import logging
from flask import Flask, render_template, flash, redirect, url_for, request
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user, login_user, login_required, logout_user
from flask_wtf import Flask<PERSON>orm
from wtforms import StringField, PasswordField, EmailField, TextAreaField, SelectField, DateField
from wtforms.validators import DataRequired, Email, Length, EqualTo, Optional
from datetime import datetime, timezone
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from api.models.base import AssessmentDB, CampaignDB, TestCaseDB
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.environ.get("SESSION_SECRET")

# Configure database
database_url = os.environ.get("DATABASE_URL")
if not database_url:
    logger.critical("DATABASE_URL environment variable is not set!")
    raise ValueError("DATABASE_URL environment variable is not set!")

app.config["SQLALCHEMY_DATABASE_URI"] = database_url
app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
    "pool_recycle": 300,
    "pool_pre_ping": True,
}

# Initialize database
db = SQLAlchemy(app)

class User(UserMixin, db.Model):
    """User model for Flask UI."""
    __tablename__ = 'flask_users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256))
    created_at = db.Column(db.DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))

    def set_password(self, password):
        """Set the password hash."""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check the password hash."""
        return check_password_hash(self.password_hash, password)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    """Load user by ID for Flask-Login."""
    return db.session.get(User, int(user_id))

# Create all tables
with app.app_context():
    try:
        db.create_all()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {str(e)}")
        raise

class SettingsForm(FlaskForm):
    """Form for user settings."""
    username = StringField('Username', validators=[
        DataRequired(),
        Length(min=3, max=64, message='Username must be between 3 and 64 characters')
    ])
    email = EmailField('Email', validators=[
        DataRequired(),
        Email(message='Invalid email address'),
        Length(max=120)
    ])
    current_password = PasswordField('Current Password', validators=[DataRequired()])
    new_password = PasswordField('New Password', validators=[
        Optional(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])

@app.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """Handle user settings updates."""
    form = SettingsForm(obj=current_user)

    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('Current password is incorrect.', 'danger')
            return render_template('settings.html', form=form)

        try:
            # Check if username is being changed and is not taken
            if form.username.data != current_user.username:
                existing_user = User.query.filter_by(username=form.username.data).first()
                if existing_user:
                    flash('Username already exists.', 'danger')
                    return render_template('settings.html', form=form)
                current_user.username = form.username.data

            # Check if email is being changed and is not taken
            if form.email.data != current_user.email:
                existing_email = User.query.filter_by(email=form.email.data).first()
                if existing_email:
                    flash('Email already registered.', 'danger')
                    return render_template('settings.html', form=form)
                current_user.email = form.email.data

            # Update password if provided
            if form.new_password.data:
                current_user.set_password(form.new_password.data)

            db.session.commit()
            flash('Settings updated successfully.', 'success')
            return redirect(url_for('settings'))

        except Exception as e:
            logger.error(f"Error updating settings: {str(e)}")
            db.session.rollback()
            flash('An error occurred while updating settings. Please try again.', 'danger')

    return render_template('settings.html', form=form)

@app.route('/')
def index():
    """Render the main dashboard page."""
    logger.debug("Rendering dashboard page")
    return render_template('dashboard.html')

@app.route('/documentation')
def documentation_index():
    """Render the documentation page."""
    logger.debug("Rendering documentation page")
    return render_template('documentation.html', title='Documentation', content=render_template('documentation_content.html'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    # Get username from query parameter if available
    username = request.args.get('username', '')
    
    # If username is provided, try to get the user's email
    user_email = ''
    if username:
        user = User.query.filter_by(username=username).first()
        if user:
            user_email = user.email

    # Create form and pre-fill email if available
    form = LoginForm()
    if request.method == 'GET' and user_email:
        form.email.data = user_email

    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            flash('Logged in successfully.', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        flash('Invalid email or password.', 'danger')
    return render_template('login.html', form=form, username=username)

@app.route('/logout')
@login_required
def logout():
    """Handle user logout."""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    """Handle user registration."""
    logger.debug("Registration route accessed, method: %s", request.method)

    # Always allow access to registration page for GET requests
    if request.method == 'GET':
        logger.debug("Rendering registration page")
        return render_template('registration.html', form=RegistrationForm())

    # For POST requests, process the registration
    form = RegistrationForm()
    if form.validate_on_submit():
        try:
            # Check if user already exists
            existing_user = User.query.filter_by(
                username=form.username.data
            ).first()
            if existing_user:
                flash('Username already exists.', 'danger')
                return render_template('registration.html', form=form)

            existing_email = User.query.filter_by(
                email=form.email.data
            ).first()
            if existing_email:
                flash('Email already exists.', 'danger')
                return render_template('registration.html', form=form)

            # Create new user
            user = User(
                username=form.username.data,
                email=form.email.data
            )
            user.set_password(form.password.data)
            db.session.add(user)
            db.session.commit()
            
            flash('Registration successful! You can now log in.', 'success')
            # Redirect to login page with username as parameter
            return redirect(url_for('login', username=form.username.data))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error during registration: {str(e)}", exc_info=True)
            flash('An error occurred during registration. Please try again.', 'danger')
            return render_template('registration.html', form=form)
    
    return render_template('registration.html', form=form)

@app.route('/health')
def health():
    """Health check endpoint."""
    try:
        # Test database connection
        db.session.execute("SELECT 1")
        db.session.commit()
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {"status": "unhealthy", "error": str(e)}, 503

class LoginForm(FlaskForm):
    """Form for user login."""
    email = EmailField('Email', validators=[
        DataRequired(),
        Email(message='Invalid email address')
    ])
    password = PasswordField('Password', validators=[DataRequired()])

class RegistrationForm(FlaskForm):
    """Form for user registration."""
    username = StringField('Username', validators=[
        DataRequired(),
        Length(min=3, max=64, message='Username must be between 3 and 64 characters')
    ])
    email = EmailField('Email', validators=[
        DataRequired(),
        Email(message='Invalid email address'),
        Length(max=120)
    ])
    password = PasswordField('Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(),
        EqualTo('password', message='Passwords must match')
    ])

class AssessmentForm(FlaskForm):
    """Form for creating and editing assessments."""
    name = StringField('Assessment Name', validators=[
        DataRequired(),
        Length(min=3, max=100, message='Name must be between 3 and 100 characters')
    ])
    description = TextAreaField('Description')
    target_system = StringField('Target System', validators=[
        DataRequired(),
        Length(min=3, max=100, message='Target system must be between 3 and 100 characters')
    ])
    assessment_type = SelectField('Assessment Type', choices=[
        ('vulnerability', 'Vulnerability Assessment'),
        ('penetration', 'Penetration Test'),
        ('code_review', 'Code Review'),
        ('compliance', 'Compliance Audit')
    ], validators=[DataRequired()])
    start_date = DateField('Start Date', format='%Y-%m-%d', validators=[DataRequired()])
    end_date = DateField('End Date', format='%Y-%m-%d')
    status = SelectField('Status', choices=[
        ('planned', 'Planned'),
        ('in-progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], validators=[DataRequired()])

class CampaignForm(FlaskForm):
    """Form for creating and editing campaigns."""
    name = StringField('Campaign Name', validators=[
        DataRequired(),
        Length(min=3, max=100, message='Name must be between 3 and 100 characters')
    ])
    description = TextAreaField('Description')
    status = SelectField('Status', choices=[
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('completed', 'Completed')
    ], validators=[DataRequired()])

class TestCaseForm(FlaskForm):
    """Form for creating and editing test cases."""
    name = StringField('Test Case Name', validators=[
        DataRequired(),
        Length(min=3, max=100, message='Name must be between 3 and 100 characters')
    ])
    description = TextAreaField('Description')
    campaign_id = SelectField('Campaign', coerce=int, validators=[DataRequired()])
    expected_result = TextAreaField('Expected Result', validators=[DataRequired()])
    actual_result = TextAreaField('Actual Result')
    status = SelectField('Status', choices=[
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('passed', 'Passed'),
        ('failed', 'Failed')
    ], validators=[DataRequired()])

# Assessment routes
@app.route('/assessments')
@login_required
def assessments():
    """Render the assessments page."""
    logger.debug("Rendering assessments page")
    assessments = db.session.query(AssessmentDB).order_by(desc(AssessmentDB.created_at)).all()
    return render_template('assessments.html', assessments=assessments)

@app.route('/assessments/new', methods=['GET', 'POST'])
@login_required
def new_assessment():
    """Handle new assessment creation."""
    form = AssessmentForm()
    if form.validate_on_submit():
        try:
            assessment = AssessmentDB(
                name=form.name.data,
                description=form.description.data,
                target_system=form.target_system.data,
                assessment_type=form.assessment_type.data,
                start_date=form.start_date.data,
                end_date=form.end_date.data,
                status=form.status.data,
                created_by=current_user.id
            )
            db.session.add(assessment)
            db.session.commit()
            flash('Assessment created successfully.', 'success')
            return redirect(url_for('assessments'))
        except SQLAlchemyError as e:
            logger.error(f"Error creating assessment: {str(e)}")
            db.session.rollback()
            flash('An error occurred while creating the assessment.', 'danger')
    return render_template('assessment_form.html', form=form, title='New Assessment')

@app.route('/assessments/<int:id>')
@login_required
def view_assessment(id):
    """Render the assessment details page."""
    assessment = db.session.query(AssessmentDB).filter_by(id=id).first_or_404()
    return render_template('assessment_detail.html', assessment=assessment)

@app.route('/assessments/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_assessment(id):
    """Handle assessment editing."""
    assessment = db.session.query(AssessmentDB).filter_by(id=id).first_or_404()
    form = AssessmentForm(obj=assessment)
    if form.validate_on_submit():
        try:
            assessment.name = form.name.data
            assessment.description = form.description.data
            assessment.target_system = form.target_system.data
            assessment.assessment_type = form.assessment_type.data
            assessment.start_date = form.start_date.data
            assessment.end_date = form.end_date.data
            assessment.status = form.status.data
            db.session.commit()
            flash('Assessment updated successfully.', 'success')
            return redirect(url_for('assessments'))
        except SQLAlchemyError as e:
            logger.error(f"Error updating assessment: {str(e)}")
            db.session.rollback()
            flash('An error occurred while updating the assessment.', 'danger')
    return render_template('assessment_form.html', form=form, title='Edit Assessment')

@app.route('/assessments/<int:id>/delete', methods=['POST'])
@login_required
def delete_assessment(id):
    """Handle assessment deletion."""
    assessment = db.session.query(AssessmentDB).filter_by(id=id).first_or_404()
    try:
        db.session.delete(assessment)
        db.session.commit()
        flash('Assessment deleted successfully.', 'success')
    except SQLAlchemyError as e:
        logger.error(f"Error deleting assessment: {str(e)}")
        db.session.rollback()
        flash('An error occurred while deleting the assessment.', 'danger')
    return redirect(url_for('assessments'))

# Campaign routes
@app.route('/campaigns')
@login_required
def campaigns():
    """List all campaigns."""
    campaigns = db.session.query(CampaignDB).filter(
        CampaignDB.deleted_time.is_(None)
    ).order_by(desc(CampaignDB.created_time)).all()
    return render_template('campaigns/index.html', campaigns=campaigns)

@app.route('/campaigns/new', methods=['GET', 'POST'])
@login_required
def new_campaign():
    """Create a new campaign."""
    form = CampaignForm()
    
    if form.validate_on_submit():
        try:
            campaign = CampaignDB(
                name=form.name.data,
                description=form.description.data,
                status=form.status.data
            )
            db.session.add(campaign)
            db.session.commit()
            flash('Campaign created successfully!', 'success')
            return redirect(url_for('campaigns'))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error creating campaign: {str(e)}', 'danger')
    
    return render_template('campaigns/new.html', form=form)

@app.route('/campaigns/<int:id>')
@login_required
def view_campaign(id):
    """View a specific campaign."""
    campaign = db.session.query(CampaignDB).filter_by(id=id).first_or_404()
    return render_template('campaigns/view.html', campaign=campaign)

@app.route('/campaigns/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_campaign(id):
    """Edit a campaign."""
    campaign = db.session.query(CampaignDB).filter_by(id=id).first_or_404()
    form = CampaignForm(obj=campaign)
    
    if form.validate_on_submit():
        try:
            campaign.name = form.name.data
            campaign.description = form.description.data
            campaign.status = form.status.data
            db.session.commit()
            flash('Campaign updated successfully!', 'success')
            return redirect(url_for('view_campaign', id=campaign.id))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error updating campaign: {str(e)}', 'danger')
    
    return render_template('campaigns/edit.html', form=form, campaign=campaign)

@app.route('/campaigns/<int:id>/delete', methods=['POST'])
@login_required
def delete_campaign(id):
    """Delete a campaign."""
    campaign = db.session.query(CampaignDB).filter_by(id=id).first_or_404()
    try:
        campaign.soft_delete(db.session)
        flash('Campaign deleted successfully!', 'success')
    except SQLAlchemyError as e:
        db.session.rollback()
        flash(f'Error deleting campaign: {str(e)}', 'danger')
    
    return redirect(url_for('campaigns'))

# TestCase routes
@app.route('/testcases')
@login_required
def testcases():
    """List all test cases."""
    testcases = db.session.query(TestCaseDB).filter(
        TestCaseDB.deleted_time.is_(None)
    ).order_by(desc(TestCaseDB.created_time)).all()
    return render_template('testcases/index.html', testcases=testcases)

@app.route('/testcases/new', methods=['GET', 'POST'])
@login_required
def new_testcase():
    """Create a new test case."""
    form = TestCaseForm()
    # Populate campaign choices
    form.campaign_id.choices = [
        (c.id, c.name) for c in db.session.query(CampaignDB).filter(
            CampaignDB.deleted_time.is_(None)
        ).order_by(CampaignDB.name).all()
    ]
    
    if form.validate_on_submit():
        try:
            testcase = TestCaseDB(
                name=form.name.data,
                description=form.description.data,
                campaign_id=form.campaign_id.data,
                expected_result=form.expected_result.data,
                actual_result=form.actual_result.data,
                status=form.status.data
            )
            db.session.add(testcase)
            db.session.commit()
            flash('Test case created successfully!', 'success')
            return redirect(url_for('testcases'))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error creating test case: {str(e)}', 'danger')
    
    return render_template('testcases/new.html', form=form)

@app.route('/testcases/<int:id>')
@login_required
def view_testcase(id):
    """View a specific test case."""
    testcase = db.session.query(TestCaseDB).filter_by(id=id).first_or_404()
    return render_template('testcases/view.html', testcase=testcase)

@app.route('/testcases/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_testcase(id):
    """Edit a test case."""
    testcase = db.session.query(TestCaseDB).filter_by(id=id).first_or_404()
    form = TestCaseForm(obj=testcase)
    # Populate campaign choices
    form.campaign_id.choices = [
        (c.id, c.name) for c in db.session.query(CampaignDB).filter(
            CampaignDB.deleted_time.is_(None)
        ).order_by(CampaignDB.name).all()
    ]
    
    if form.validate_on_submit():
        try:
            testcase.name = form.name.data
            testcase.description = form.description.data
            testcase.campaign_id = form.campaign_id.data
            testcase.expected_result = form.expected_result.data
            testcase.actual_result = form.actual_result.data
            testcase.status = form.status.data
            db.session.commit()
            flash('Test case updated successfully!', 'success')
            return redirect(url_for('view_testcase', id=testcase.id))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error updating test case: {str(e)}', 'danger')
    
    return render_template('testcases/edit.html', form=form, testcase=testcase)

@app.route('/testcases/<int:id>/delete', methods=['POST'])
@login_required
def delete_testcase(id):
    """Delete a test case."""
    testcase = db.session.query(TestCaseDB).filter_by(id=id).first_or_404()
    try:
        testcase.soft_delete(db.session)
        flash('Test case deleted successfully!', 'success')
    except SQLAlchemyError as e:
        db.session.rollback()
        flash(f'Error deleting test case: {str(e)}', 'danger')
    
    return redirect(url_for('testcases'))

if __name__ == '__main__':
    # Always serve the Flask UI on port 5000
    port = int(os.environ.get("PORT", 5000))
    logger.info(f"Starting Flask UI server on port {port}")
    app.run(host='0.0.0.0', port=port, debug=True)