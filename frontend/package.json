{"name": "regression-rigor-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@heroicons/react": "^2.1.1", "@mui/material": "^5.15.10", "@mui/icons-material": "^5.15.10", "@types/node": "^20.11.19", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "axios": "^1.6.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1", "typescript": "^5.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/axios": "^0.14.0", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "http-proxy-middleware": "^2.0.6", "react-scripts": "5.0.1"}, "proxy": "http://localhost:5000"}