import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Collapse,
  IconButton,
  Alert
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  CheckCircle as SuccessIcon,
  <PERSON>rror as <PERSON>rrorIcon,
  Pause as PauseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { ChainExecution, NodeExecution, ExecutionStatus } from '../../types/testcaseChaining';
import { getChainExecution, getNodeExecutions } from '../../services/testcaseChaining';

interface ChainExecutionStatusProps {
  executionId: string;
  onExecutionComplete?: (execution: ChainExecution) => void;
}

const ChainExecutionStatus: React.FC<ChainExecutionStatusProps> = ({
  executionId,
  onExecutionComplete
}) => {
  const [execution, setExecution] = useState<ChainExecution | null>(null);
  const [nodeExecutions, setNodeExecutions] = useState<NodeExecution[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState<boolean>(true);

  useEffect(() => {
    const fetchExecutionData = async () => {
      try {
        setLoading(true);
        const executionData = await getChainExecution(executionId);
        const nodeExecutionData = await getNodeExecutions(executionId);
        
        setExecution(executionData);
        setNodeExecutions(nodeExecutionData);
        setError(null);

        // Check if execution is complete
        if (executionData.status === ExecutionStatus.COMPLETED || 
            executionData.status === ExecutionStatus.FAILED) {
          onExecutionComplete?.(executionData);
        }
      } catch (err) {
        console.error(err);
        setError('Failed to fetch execution data');
      } finally {
        setLoading(false);
      }
    };

    fetchExecutionData();

    // Poll for updates if execution is still running
    const interval = setInterval(() => {
      if (execution?.status === ExecutionStatus.RUNNING || 
          execution?.status === ExecutionStatus.PENDING) {
        fetchExecutionData();
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [executionId, execution?.status, onExecutionComplete]);

  const getStatusIcon = (status: ExecutionStatus) => {
    switch (status) {
      case ExecutionStatus.COMPLETED:
        return <SuccessIcon color="success" />;
      case ExecutionStatus.FAILED:
        return <ErrorIcon color="error" />;
      case ExecutionStatus.RUNNING:
        return <PlayIcon color="primary" />;
      case ExecutionStatus.PENDING:
        return <PauseIcon color="warning" />;
      default:
        return <PauseIcon />;
    }
  };

  const getStatusColor = (status: ExecutionStatus) => {
    switch (status) {
      case ExecutionStatus.COMPLETED:
        return 'success';
      case ExecutionStatus.FAILED:
        return 'error';
      case ExecutionStatus.RUNNING:
        return 'primary';
      case ExecutionStatus.PENDING:
        return 'warning';
      default:
        return 'default';
    }
  };

  const calculateProgress = () => {
    if (!nodeExecutions.length) return 0;
    
    const completedNodes = nodeExecutions.filter(
      node => node.status === ExecutionStatus.COMPLETED || node.status === ExecutionStatus.FAILED
    ).length;
    
    return (completedNodes / nodeExecutions.length) * 100;
  };

  const getExecutionDuration = () => {
    if (!execution?.start_time) return 'N/A';
    
    const start = new Date(execution.start_time);
    const end = execution.end_time ? new Date(execution.end_time) : new Date();
    const duration = Math.round((end.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) return `${duration}s`;
    if (duration < 3600) return `${Math.floor(duration / 60)}m ${duration % 60}s`;
    return `${Math.floor(duration / 3600)}h ${Math.floor((duration % 3600) / 60)}m`;
  };

  if (loading) return <Typography>Loading execution status...</Typography>;
  if (error) return <Alert severity="error">{error}</Alert>;
  if (!execution) return <Alert severity="warning">Execution not found</Alert>;

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            Chain Execution Status
          </Typography>
          <Box display="flex" alignItems="center" gap={1}>
            <Chip
              icon={getStatusIcon(execution.status)}
              label={execution.status.toUpperCase()}
              color={getStatusColor(execution.status) as any}
              size="small"
            />
            <IconButton
              onClick={() => setExpanded(!expanded)}
              size="small"
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        <Box mb={2}>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Progress: {Math.round(calculateProgress())}% ({nodeExecutions.filter(n => 
              n.status === ExecutionStatus.COMPLETED || n.status === ExecutionStatus.FAILED
            ).length} of {nodeExecutions.length} nodes)
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={calculateProgress()} 
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        <Box display="flex" justifyContent="space-between" mb={2}>
          <Typography variant="body2" color="textSecondary">
            Started: {new Date(execution.start_time).toLocaleString()}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Duration: {getExecutionDuration()}
          </Typography>
        </Box>

        <Collapse in={expanded}>
          <Typography variant="subtitle2" gutterBottom>
            Node Execution Details:
          </Typography>
          <List dense>
            {nodeExecutions.map((nodeExecution) => (
              <ListItem key={nodeExecution.id}>
                <ListItemIcon>
                  {getStatusIcon(nodeExecution.status)}
                </ListItemIcon>
                <ListItemText
                  primary={nodeExecution.node?.testcase?.name || `Node ${nodeExecution.node_id}`}
                  secondary={
                    <Box>
                      <Typography variant="caption" display="block">
                        Status: {nodeExecution.status}
                      </Typography>
                      {nodeExecution.start_time && (
                        <Typography variant="caption" display="block">
                          Started: {new Date(nodeExecution.start_time).toLocaleString()}
                        </Typography>
                      )}
                      {nodeExecution.end_time && (
                        <Typography variant="caption" display="block">
                          Completed: {new Date(nodeExecution.end_time).toLocaleString()}
                        </Typography>
                      )}
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default ChainExecutionStatus;
