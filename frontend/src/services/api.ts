import axios, { AxiosError } from 'axios';
import { Campaign, CampaignList, CampaignTestCase } from '../types/campaign';

const api = axios.create({
  baseURL: '/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, config.data);
    return config;
  },
  (error) => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging
api.interceptors.response.use(
  (response) => {
    console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data);
    return response;
  },
  (error: AxiosError) => {
    console.error('[API Response Error]', {
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
    });
    return Promise.reject(error);
  }
);

export const campaignApi = {
  list: async (): Promise<CampaignList> => {
    try {
      const response = await api.get('/campaigns');
      return response.data;
    } catch (error) {
      console.error('[Campaign API] Failed to fetch campaigns:', error);
      throw error;
    }
  },

  get: async (id: string): Promise<Campaign> => {
    try {
      const response = await api.get(`/campaigns/${id}`);
      return response.data;
    } catch (error) {
      console.error(`[Campaign API] Failed to fetch campaign ${id}:`, error);
      throw error;
    }
  },

  create: async (data: Partial<Campaign>): Promise<Campaign> => {
    try {
      const response = await api.post('/campaigns', data);
      return response.data;
    } catch (error) {
      console.error('[Campaign API] Failed to create campaign:', error);
      throw error;
    }
  },

  update: async (id: string, data: Partial<Campaign>): Promise<Campaign> => {
    try {
      const response = await api.put(`/campaigns/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`[Campaign API] Failed to update campaign ${id}:`, error);
      throw error;
    }
  },

  delete: async (id: string): Promise<void> => {
    try {
      await api.delete(`/campaigns/${id}`);
    } catch (error) {
      console.error(`[Campaign API] Failed to delete campaign ${id}:`, error);
      throw error;
    }
  },

  getTestCases: async (campaignId: string): Promise<CampaignTestCase[]> => {
    try {
      const response = await api.get(`/campaigns/${campaignId}/test-cases`);
      return response.data;
    } catch (error) {
      console.error(`[Campaign API] Failed to fetch test cases for campaign ${campaignId}:`, error);
      throw error;
    }
  },

  addTestCase: async (campaignId: string, data: Partial<CampaignTestCase>): Promise<CampaignTestCase> => {
    try {
      const response = await api.post(`/campaigns/${campaignId}/test-cases`, data);
      return response.data;
    } catch (error) {
      console.error(`[Campaign API] Failed to add test case to campaign ${campaignId}:`, error);
      throw error;
    }
  },

  updateTestCase: async (
    campaignId: string,
    testCaseId: string,
    data: Partial<CampaignTestCase>
  ): Promise<CampaignTestCase> => {
    try {
      const response = await api.put(`/campaigns/${campaignId}/test-cases/${testCaseId}`, data);
      return response.data;
    } catch (error) {
      console.error(`[Campaign API] Failed to update test case ${testCaseId} in campaign ${campaignId}:`, error);
      throw error;
    }
  },

  deleteTestCase: async (campaignId: string, testCaseId: string): Promise<void> => {
    try {
      await api.delete(`/campaigns/${campaignId}/test-cases/${testCaseId}`);
    } catch (error) {
      console.error(`[Campaign API] Failed to delete test case ${testCaseId} from campaign ${campaignId}:`, error);
      throw error;
    }
  },
}; 