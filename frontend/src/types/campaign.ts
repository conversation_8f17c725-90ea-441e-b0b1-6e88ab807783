export interface Campaign {
  id: number;
  name: string;
  description?: string;
  status: 'draft' | 'active' | 'completed' | 'archived';
  start_date: string;
  end_date: string;
  created_by: number;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  test_cases: CampaignTestCase[];
}

export interface CampaignTestCase {
  id: number;
  campaign_id: number;
  test_case_id: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  assigned_to?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CampaignList {
  total: number;
  items: Campaign[];
} 