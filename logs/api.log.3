{"timestamp": "2025-04-01T16:35:28.839208", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015493, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE users (\n\tid VARCHAR NOT NULL, \n\tusername VARCHAR NOT NULL, \n\temail VARCHAR NOT NULL, \n\thashed_password VARCHAR(256) NOT NULL, \n\tis_active BOOLEAN, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\temail_verified BOOLEAN, \n\trole userrole NOT NULL, \n\tfull_name VARCHAR(100), \n\tbio TEXT, \n\tlast_login TIMESTAMP WITH TIME ZONE, \n\tfailed_login_attempts INTEGER, \n\tpassword_changed_at TIMESTAMP WITH TIME ZONE, \n\taccount_locked BOOLEAN, \n\taccount_locked_at TIMESTAMP WITH TIME ZONE, \n\tlast_failed_login TIMESTAMP WITH TIME ZONE, \n\tpassword_reset_token VARCHAR(256), \n\tpassword_reset_expires TIMESTAMP WITH TIME ZONE, \n\ttwo_factor_enabled BOOLEAN, \n\ttwo_factor_secret VARCHAR(32), \n\tbackup_codes VARCHAR(512), \n\tCONSTRAINT pk_users PRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:28.843942", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE UNIQUE INDEX ix_users_username ON users (username)"}
{"timestamp": "2025-04-01T16:35:28.858677", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.011801, "executemany": false, "parameters": {}, "statement": "CREATE UNIQUE INDEX ix_users_username ON users (username)"}
{"timestamp": "2025-04-01T16:35:28.863195", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE UNIQUE INDEX ix_users_email ON users (email)"}
{"timestamp": "2025-04-01T16:35:28.873068", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009373, "executemany": false, "parameters": {}, "statement": "CREATE UNIQUE INDEX ix_users_email ON users (email)"}
{"timestamp": "2025-04-01T16:35:28.878196", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE organizations (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(1000), \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tversion VARCHAR(10), \n\tCONSTRAINT pk_organizations PRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:28.895473", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.016032, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE organizations (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(1000), \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tversion VARCHAR(10), \n\tCONSTRAINT pk_organizations PRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:28.900416", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_versions (\n\tid SERIAL NOT NULL, \n\tversion VARCHAR NOT NULL, \n\timport_date TIMESTAMP WITHOUT TIME ZONE, \n\tis_current BOOLEAN, \n\ttechnology_domain technologydomain NOT NULL, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_versions PRIMARY KEY (id), \n\tCONSTRAINT uq_mitre_versions_version UNIQUE (version)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:28.922577", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.021197, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_versions (\n\tid SERIAL NOT NULL, \n\tversion VARCHAR NOT NULL, \n\timport_date TIMESTAMP WITHOUT TIME ZONE, \n\tis_current BOOLEAN, \n\ttechnology_domain technologydomain NOT NULL, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_versions PRIMARY KEY (id), \n\tCONSTRAINT uq_mitre_versions_version UNIQUE (version)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:28.927279", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_versions (\n\tid SERIAL NOT NULL, \n\tversion VARCHAR(50) NOT NULL, \n\tname VARCHAR(100) NOT NULL, \n\tdescription TEXT, \n\timport_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tis_current BOOLEAN NOT NULL, \n\tCONSTRAINT pk_atlas_versions PRIMARY KEY (id), \n\tCONSTRAINT uq_atlas_versions_version UNIQUE (version)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:28.947620", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.019912, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_versions (\n\tid SERIAL NOT NULL, \n\tversion VARCHAR(50) NOT NULL, \n\tname VARCHAR(100) NOT NULL, \n\tdescription TEXT, \n\timport_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tis_current BOOLEAN NOT NULL, \n\tCONSTRAINT pk_atlas_versions PRIMARY KEY (id), \n\tCONSTRAINT uq_atlas_versions_version UNIQUE (version)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:28.952111", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE stix_objects (\n\tstix_id VARCHAR NOT NULL, \n\ttype VARCHAR NOT NULL, \n\tdata JSON NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_stix_objects PRIMARY KEY (stix_id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:28.967662", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.014852, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE stix_objects (\n\tstix_id VARCHAR NOT NULL, \n\ttype VARCHAR NOT NULL, \n\tdata JSON NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_stix_objects PRIMARY KEY (stix_id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:28.972953", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_stix_objects_type ON stix_objects (type)"}
{"timestamp": "2025-04-01T16:35:28.983536", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.010633, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_stix_objects_type ON stix_objects (type)"}
{"timestamp": "2025-04-01T16:35:28.988302", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE error_handlings (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(100) NOT NULL, \n\tdescription TEXT, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tupdated_at TIMESTAMP WITH TIME ZONE, \n\terror_code VARCHAR(50), \n\terror_type VARCHAR(100) NOT NULL, \n\terror_message TEXT NOT NULL, \n\tis_user_facing BOOLEAN, \n\thttp_status_code INTEGER, \n\tseverity VARCHAR(50), \n\tCONSTRAINT pk_error_handlings PRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.004121", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015319, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE error_handlings (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(100) NOT NULL, \n\tdescription TEXT, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tupdated_at TIMESTAMP WITH TIME ZONE, \n\terror_code VARCHAR(50), \n\terror_type VARCHAR(100) NOT NULL, \n\terror_message TEXT NOT NULL, \n\tis_user_facing BOOLEAN, \n\thttp_status_code INTEGER, \n\tseverity VARCHAR(50), \n\tCONSTRAINT pk_error_handlings PRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.008931", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_error_handlings_id ON error_handlings (id)"}
{"timestamp": "2025-04-01T16:35:29.018523", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009545, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_error_handlings_id ON error_handlings (id)"}
{"timestamp": "2025-04-01T16:35:29.023463", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3fend_versions (\n\tid SERIAL NOT NULL, \n\tversion VARCHAR(50) NOT NULL, \n\timport_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tis_current BOOLEAN NOT NULL, \n\tCONSTRAINT pk_d3fend_versions PRIMARY KEY (id), \n\tCONSTRAINT uq_d3fend_versions_version UNIQUE (version)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.038984", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015517, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3fend_versions (\n\tid SERIAL NOT NULL, \n\tversion VARCHAR(50) NOT NULL, \n\timport_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tis_current BOOLEAN NOT NULL, \n\tCONSTRAINT pk_d3fend_versions PRIMARY KEY (id), \n\tCONSTRAINT uq_d3fend_versions_version UNIQUE (version)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.043422", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_classes (\n\tid SERIAL NOT NULL, \n\turi VARCHAR(255) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tCONSTRAINT pk_d3f_classes PRIMARY KEY (id), \n\tCONSTRAINT uq_d3f_classes_uri UNIQUE (uri)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.064018", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.020377, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_classes (\n\tid SERIAL NOT NULL, \n\turi VARCHAR(255) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tCONSTRAINT pk_d3f_classes PRIMARY KEY (id), \n\tCONSTRAINT uq_d3f_classes_uri UNIQUE (uri)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.068209", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_properties (\n\tid SERIAL NOT NULL, \n\turi VARCHAR(255) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tproperty_type VARCHAR(50) NOT NULL, \n\tdescription TEXT, \n\tCONSTRAINT pk_d3f_properties PRIMARY KEY (id), \n\tCONSTRAINT uq_d3f_properties_uri UNIQUE (uri)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.087793", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.018873, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_properties (\n\tid SERIAL NOT NULL, \n\turi VARCHAR(255) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tproperty_type VARCHAR(50) NOT NULL, \n\tdescription TEXT, \n\tCONSTRAINT pk_d3f_properties PRIMARY KEY (id), \n\tCONSTRAINT uq_d3f_properties_uri UNIQUE (uri)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.092188", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3fend_mappings (\n\tid SERIAL NOT NULL, \n\ttechnique_id VARCHAR NOT NULL, \n\tname VARCHAR NOT NULL, \n\teffectiveness_score FLOAT NOT NULL, \n\tdescription TEXT, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tCONSTRAINT pk_d3fend_mappings PRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.107213", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.014262, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3fend_mappings (\n\tid SERIAL NOT NULL, \n\ttechnique_id VARCHAR NOT NULL, \n\tname VARCHAR NOT NULL, \n\teffectiveness_score FLOAT NOT NULL, \n\tdescription TEXT, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tCONSTRAINT pk_d3fend_mappings PRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.111506", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_d3fend_mappings_id ON d3fend_mappings (id)"}
{"timestamp": "2025-04-01T16:35:29.121118", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009509, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_d3fend_mappings_id ON d3fend_mappings (id)"}
{"timestamp": "2025-04-01T16:35:29.125323", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_d3fend_mappings_technique_id ON d3fend_mappings (technique_id)"}
{"timestamp": "2025-04-01T16:35:29.134834", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.008648, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_d3fend_mappings_technique_id ON d3fend_mappings (technique_id)"}
{"timestamp": "2025-04-01T16:35:29.139468", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_defense_versions (\n\tid SERIAL NOT NULL, \n\tversion VARCHAR(50) NOT NULL, \n\timport_date TIMESTAMP WITHOUT TIME ZONE, \n\tis_current BOOLEAN, \n\tCONSTRAINT pk_mitre_defense_versions PRIMARY KEY (id), \n\tCONSTRAINT uq_mitre_defense_versions_version UNIQUE (version)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.155085", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015243, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_defense_versions (\n\tid SERIAL NOT NULL, \n\tversion VARCHAR(50) NOT NULL, \n\timport_date TIMESTAMP WITHOUT TIME ZONE, \n\tis_current BOOLEAN, \n\tCONSTRAINT pk_mitre_defense_versions PRIMARY KEY (id), \n\tCONSTRAINT uq_mitre_defense_versions_version UNIQUE (version)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.159776", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE stix_d3fend_mappings (\n\tid SERIAL NOT NULL, \n\tstix_id VARCHAR(50) NOT NULL, \n\td3fend_id VARCHAR(50) NOT NULL, \n\ttechnique_id VARCHAR(50) NOT NULL, \n\tmapping_type VARCHAR(50) NOT NULL, \n\tconfidence FLOAT, \n\tcountermeasure_name VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(1000), \n\teffectiveness_score FLOAT, \n\tmapping_metadata JSON, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_stix_d3fend_mappings PRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.175785", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015169, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE stix_d3fend_mappings (\n\tid SERIAL NOT NULL, \n\tstix_id VARCHAR(50) NOT NULL, \n\td3fend_id VARCHAR(50) NOT NULL, \n\ttechnique_id VARCHAR(50) NOT NULL, \n\tmapping_type VARCHAR(50) NOT NULL, \n\tconfidence FLOAT, \n\tcountermeasure_name VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(1000), \n\teffectiveness_score FLOAT, \n\tmapping_metadata JSON, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_stix_d3fend_mappings PRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.180290", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_stix_d3fend_mappings_technique_id ON stix_d3fend_mappings (technique_id)"}
{"timestamp": "2025-04-01T16:35:29.192132", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009884, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_stix_d3fend_mappings_technique_id ON stix_d3fend_mappings (technique_id)"}
{"timestamp": "2025-04-01T16:35:29.196862", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE user_sessions (\n\tid VARCHAR NOT NULL, \n\tuser_id VARCHAR NOT NULL, \n\tdevice_id VARCHAR, \n\tsession_token VARCHAR NOT NULL, \n\tis_active BOOLEAN, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tlast_activity TIMESTAMP WITH TIME ZONE, \n\texpires_at TIMESTAMP WITH TIME ZONE NOT NULL, \n\tlogout_at TIMESTAMP WITH TIME ZONE, \n\tip_address VARCHAR, \n\tuser_agent VARCHAR, \n\tCONSTRAINT pk_user_sessions PRIMARY KEY (id), \n\tCONSTRAINT fk_user_sessions_user_id_users FOREIGN KEY(user_id) REFERENCES users (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_user_sessions_device_id_device_info FOREIGN KEY(device_id) REFERENCES device_info (id) ON DELETE SET NULL, \n\tCONSTRAINT uq_user_sessions_session_token UNIQUE (session_token)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.219006", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.021037, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE user_sessions (\n\tid VARCHAR NOT NULL, \n\tuser_id VARCHAR NOT NULL, \n\tdevice_id VARCHAR, \n\tsession_token VARCHAR NOT NULL, \n\tis_active BOOLEAN, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tlast_activity TIMESTAMP WITH TIME ZONE, \n\texpires_at TIMESTAMP WITH TIME ZONE NOT NULL, \n\tlogout_at TIMESTAMP WITH TIME ZONE, \n\tip_address VARCHAR, \n\tuser_agent VARCHAR, \n\tCONSTRAINT pk_user_sessions PRIMARY KEY (id), \n\tCONSTRAINT fk_user_sessions_user_id_users FOREIGN KEY(user_id) REFERENCES users (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_user_sessions_device_id_device_info FOREIGN KEY(device_id) REFERENCES device_info (id) ON DELETE SET NULL, \n\tCONSTRAINT uq_user_sessions_session_token UNIQUE (session_token)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.224739", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE user_preferences (\n\tid VARCHAR NOT NULL, \n\tuser_id VARCHAR NOT NULL, \n\ttheme VARCHAR(20), \n\tnotification_settings JSON, \n\tdashboard_layout JSON, \n\ttimezone VARCHAR(50), \n\tlanguage VARCHAR(10), \n\temail_notifications BOOLEAN, \n\ttwo_factor_enabled BOOLEAN, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tupdated_at TIMESTAMP WITH TIME ZONE, \n\tCONSTRAINT pk_user_preferences PRIMARY KEY (id), \n\tCONSTRAINT uq_user_preferences_user_id UNIQUE (user_id), \n\tCONSTRAINT fk_user_preferences_user_id_users FOREIGN KEY(user_id) REFERENCES users (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.246459", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.022014, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE user_preferences (\n\tid VARCHAR NOT NULL, \n\tuser_id VARCHAR NOT NULL, \n\ttheme VARCHAR(20), \n\tnotification_settings JSON, \n\tdashboard_layout JSON, \n\ttimezone VARCHAR(50), \n\tlanguage VARCHAR(10), \n\temail_notifications BOOLEAN, \n\ttwo_factor_enabled BOOLEAN, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tupdated_at TIMESTAMP WITH TIME ZONE, \n\tCONSTRAINT pk_user_preferences PRIMARY KEY (id), \n\tCONSTRAINT uq_user_preferences_user_id UNIQUE (user_id), \n\tCONSTRAINT fk_user_preferences_user_id_users FOREIGN KEY(user_id) REFERENCES users (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.251010", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE user_activities (\n\tid VARCHAR NOT NULL, \n\tuser_id VARCHAR NOT NULL, \n\tactivity_type VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tactivity_metadata JSON, \n\tip_address VARCHAR, \n\tuser_agent VARCHAR, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_user_activities PRIMARY KEY (id), \n\tCONSTRAINT fk_user_activities_user_id_users FOREIGN KEY(user_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.266198", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.014909, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE user_activities (\n\tid VARCHAR NOT NULL, \n\tuser_id VARCHAR NOT NULL, \n\tactivity_type VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tactivity_metadata JSON, \n\tip_address VARCHAR, \n\tuser_agent VARCHAR, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_user_activities PRIMARY KEY (id), \n\tCONSTRAINT fk_user_activities_user_id_users FOREIGN KEY(user_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.270797", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE admin_audit_logs (\n\tid SERIAL NOT NULL, \n\tuser_id VARCHAR NOT NULL, \n\taction VARCHAR(100) NOT NULL, \n\tresource_type VARCHAR(50) NOT NULL, \n\tresource_id VARCHAR(50), \n\tdetails JSON, \n\tip_address VARCHAR(50), \n\tuser_agent VARCHAR(255), \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tCONSTRAINT pk_admin_audit_logs PRIMARY KEY (id), \n\tCONSTRAINT fk_admin_audit_logs_user_id_users FOREIGN KEY(user_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.286034", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.014963, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE admin_audit_logs (\n\tid SERIAL NOT NULL, \n\tuser_id VARCHAR NOT NULL, \n\taction VARCHAR(100) NOT NULL, \n\tresource_type VARCHAR(50) NOT NULL, \n\tresource_id VARCHAR(50), \n\tdetails JSON, \n\tip_address VARCHAR(50), \n\tuser_agent VARCHAR(255), \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tCONSTRAINT pk_admin_audit_logs PRIMARY KEY (id), \n\tCONSTRAINT fk_admin_audit_logs_user_id_users FOREIGN KEY(user_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.290573", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_admin_audit_logs_id ON admin_audit_logs (id)"}
{"timestamp": "2025-04-01T16:35:29.299969", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009211, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_admin_audit_logs_id ON admin_audit_logs (id)"}
{"timestamp": "2025-04-01T16:35:29.304711", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE admin_notifications (\n\tid SERIAL NOT NULL, \n\ttitle VARCHAR(200) NOT NULL, \n\tmessage TEXT NOT NULL, \n\tseverity VARCHAR(20) NOT NULL, \n\tis_read BOOLEAN, \n\tuser_id VARCHAR, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tCONSTRAINT pk_admin_notifications PRIMARY KEY (id), \n\tCONSTRAINT fk_admin_notifications_user_id_users FOREIGN KEY(user_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.320080", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015091, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE admin_notifications (\n\tid SERIAL NOT NULL, \n\ttitle VARCHAR(200) NOT NULL, \n\tmessage TEXT NOT NULL, \n\tseverity VARCHAR(20) NOT NULL, \n\tis_read BOOLEAN, \n\tuser_id VARCHAR, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tCONSTRAINT pk_admin_notifications PRIMARY KEY (id), \n\tCONSTRAINT fk_admin_notifications_user_id_users FOREIGN KEY(user_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.324677", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_admin_notifications_id ON admin_notifications (id)"}
{"timestamp": "2025-04-01T16:35:29.335512", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.010327, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_admin_notifications_id ON admin_notifications (id)"}
{"timestamp": "2025-04-01T16:35:29.339989", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE error_logs (\n\tid SERIAL NOT NULL, \n\terror_type VARCHAR(255) NOT NULL, \n\terror_message TEXT NOT NULL, \n\tstack_trace TEXT, \n\tuser_id VARCHAR, \n\tendpoint VARCHAR(255), \n\trequest_method VARCHAR(10), \n\trequest_data JSON, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tCONSTRAINT pk_error_logs PRIMARY KEY (id), \n\tCONSTRAINT fk_error_logs_user_id_users FOREIGN KEY(user_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.356086", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.014767, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE error_logs (\n\tid SERIAL NOT NULL, \n\terror_type VARCHAR(255) NOT NULL, \n\terror_message TEXT NOT NULL, \n\tstack_trace TEXT, \n\tuser_id VARCHAR, \n\tendpoint VARCHAR(255), \n\trequest_method VARCHAR(10), \n\trequest_data JSON, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tCONSTRAINT pk_error_logs PRIMARY KEY (id), \n\tCONSTRAINT fk_error_logs_user_id_users FOREIGN KEY(user_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.360700", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_error_logs_id ON error_logs (id)"}
{"timestamp": "2025-04-01T16:35:29.369729", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.008957, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_error_logs_id ON error_logs (id)"}
{"timestamp": "2025-04-01T16:35:29.375078", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE audit_logs (\n\tid VARCHAR NOT NULL, \n\tuser_id VARCHAR, \n\taction VARCHAR NOT NULL, \n\tresource_type VARCHAR NOT NULL, \n\tresource_id VARCHAR, \n\tdetails JSON, \n\tip_address VARCHAR, \n\tuser_agent VARCHAR, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tCONSTRAINT pk_audit_logs PRIMARY KEY (id), \n\tCONSTRAINT fk_audit_logs_user_id_users FOREIGN KEY(user_id) REFERENCES users (id) ON DELETE SET NULL\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.391852", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.016307, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE audit_logs (\n\tid VARCHAR NOT NULL, \n\tuser_id VARCHAR, \n\taction VARCHAR NOT NULL, \n\tresource_type VARCHAR NOT NULL, \n\tresource_id VARCHAR, \n\tdetails JSON, \n\tip_address VARCHAR, \n\tuser_agent VARCHAR, \n\tcreated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), \n\tCONSTRAINT pk_audit_logs PRIMARY KEY (id), \n\tCONSTRAINT fk_audit_logs_user_id_users FOREIGN KEY(user_id) REFERENCES users (id) ON DELETE SET NULL\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.397574", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE testcase_chains (\n\tid SERIAL NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tcreated_by VARCHAR NOT NULL, \n\tstatus VARCHAR DEFAULT 'draft' NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_testcase_chains PRIMARY KEY (id), \n\tCONSTRAINT fk_testcase_chains_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.413561", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.016066, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE testcase_chains (\n\tid SERIAL NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tcreated_by VARCHAR NOT NULL, \n\tstatus VARCHAR DEFAULT 'draft' NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_testcase_chains PRIMARY KEY (id), \n\tCONSTRAINT fk_testcase_chains_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.418307", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_testcase_chains_id ON testcase_chains (id)"}
{"timestamp": "2025-04-01T16:35:29.428515", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009431, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_testcase_chains_id ON testcase_chains (id)"}
{"timestamp": "2025-04-01T16:35:29.433447", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE cl_campaign_to_organization (\n\tcampaign_id INTEGER NOT NULL, \n\torganization_id INTEGER NOT NULL, \n\tCONSTRAINT pk_cl_campaign_to_organization PRIMARY KEY (campaign_id, organization_id), \n\tCONSTRAINT fk_cl_campaign_to_organization_campaign_id_cl_campaign FOREIGN KEY(campaign_id) REFERENCES cl_campaign (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_cl_campaign_to_organization_organization_id_cl_organization FOREIGN KEY(organization_id) REFERENCES cl_organization (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.446598", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.011521, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE cl_campaign_to_organization (\n\tcampaign_id INTEGER NOT NULL, \n\torganization_id INTEGER NOT NULL, \n\tCONSTRAINT pk_cl_campaign_to_organization PRIMARY KEY (campaign_id, organization_id), \n\tCONSTRAINT fk_cl_campaign_to_organization_campaign_id_cl_campaign FOREIGN KEY(campaign_id) REFERENCES cl_campaign (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_cl_campaign_to_organization_organization_id_cl_organization FOREIGN KEY(organization_id) REFERENCES cl_organization (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.452761", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE test_cases_base (\n\tid SERIAL NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tcampaign_id INTEGER NOT NULL, \n\texpected_result VARCHAR NOT NULL, \n\tactual_result VARCHAR, \n\tstatus VARCHAR DEFAULT 'pending' NOT NULL, \n\tcreated_by VARCHAR, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_test_cases_base PRIMARY KEY (id), \n\tCONSTRAINT fk_test_cases_base_campaign_id_cl_campaign FOREIGN KEY(campaign_id) REFERENCES cl_campaign (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_test_cases_base_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.471179", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.018121, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE test_cases_base (\n\tid SERIAL NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tcampaign_id INTEGER NOT NULL, \n\texpected_result VARCHAR NOT NULL, \n\tactual_result VARCHAR, \n\tstatus VARCHAR DEFAULT 'pending' NOT NULL, \n\tcreated_by VARCHAR, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_test_cases_base PRIMARY KEY (id), \n\tCONSTRAINT fk_test_cases_base_campaign_id_cl_campaign FOREIGN KEY(campaign_id) REFERENCES cl_campaign (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_test_cases_base_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.476108", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_test_cases_base_id ON test_cases_base (id)"}
{"timestamp": "2025-04-01T16:35:29.487129", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.010056, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_test_cases_base_id ON test_cases_base (id)"}
{"timestamp": "2025-04-01T16:35:29.493196", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE test_cases (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\ttype testcasetype NOT NULL, \n\tstatus testcasestatus NOT NULL, \n\tpriority testcasepriority NOT NULL, \n\tcomplexity testcasecomplexity NOT NULL, \n\tprerequisites TEXT, \n\tsteps JSONB, \n\texpected_result TEXT NOT NULL, \n\tactual_result TEXT, \n\ttags JSONB, \n\tmitre_techniques JSONB, \n\tcreated_by VARCHAR, \n\trevoked_by_id VARCHAR, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL, \n\tversion VARCHAR(10), \n\tis_deprecated BOOLEAN NOT NULL, \n\tis_revoked BOOLEAN NOT NULL, \n\tCONSTRAINT pk_test_cases PRIMARY KEY (id), \n\tCONSTRAINT fk_test_cases_created_by_users FOREIGN KEY(created_by) REFERENCES users (id), \n\tCONSTRAINT fk_test_cases_revoked_by_id_users FOREIGN KEY(revoked_by_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.509932", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.01622, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE test_cases (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\ttype testcasetype NOT NULL, \n\tstatus testcasestatus NOT NULL, \n\tpriority testcasepriority NOT NULL, \n\tcomplexity testcasecomplexity NOT NULL, \n\tprerequisites TEXT, \n\tsteps JSONB, \n\texpected_result TEXT NOT NULL, \n\tactual_result TEXT, \n\ttags JSONB, \n\tmitre_techniques JSONB, \n\tcreated_by VARCHAR, \n\trevoked_by_id VARCHAR, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL, \n\tversion VARCHAR(10), \n\tis_deprecated BOOLEAN NOT NULL, \n\tis_revoked BOOLEAN NOT NULL, \n\tCONSTRAINT pk_test_cases PRIMARY KEY (id), \n\tCONSTRAINT fk_test_cases_created_by_users FOREIGN KEY(created_by) REFERENCES users (id), \n\tCONSTRAINT fk_test_cases_revoked_by_id_users FOREIGN KEY(revoked_by_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.515012", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_test_cases_id ON test_cases (id)"}
{"timestamp": "2025-04-01T16:35:29.525410", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.010411, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_test_cases_id ON test_cases (id)"}
{"timestamp": "2025-04-01T16:35:29.530762", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_techniques (\n\tid SERIAL NOT NULL, \n\ttechnique_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(2000), \n\tversion_id INTEGER, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\textra_data JSON, \n\tdetection VARCHAR(2000), \n\tplatforms JSON, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_techniques PRIMARY KEY (id), \n\tCONSTRAINT uix_technique_version UNIQUE (technique_id, version_id), \n\tCONSTRAINT fk_mitre_techniques_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.551471", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.020784, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_techniques (\n\tid SERIAL NOT NULL, \n\ttechnique_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(2000), \n\tversion_id INTEGER, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\textra_data JSON, \n\tdetection VARCHAR(2000), \n\tplatforms JSON, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_techniques PRIMARY KEY (id), \n\tCONSTRAINT uix_technique_version UNIQUE (technique_id, version_id), \n\tCONSTRAINT fk_mitre_techniques_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.556058", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_tactics (\n\tid SERIAL NOT NULL, \n\ttactic_id VARCHAR NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tversion_id INTEGER, \n\tdata JSON, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_tactics PRIMARY KEY (id), \n\tCONSTRAINT uix_tactic_version UNIQUE (tactic_id, version_id), \n\tCONSTRAINT fk_mitre_tactics_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.577902", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.020515, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_tactics (\n\tid SERIAL NOT NULL, \n\ttactic_id VARCHAR NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tversion_id INTEGER, \n\tdata JSON, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_tactics PRIMARY KEY (id), \n\tCONSTRAINT uix_tactic_version UNIQUE (tactic_id, version_id), \n\tCONSTRAINT fk_mitre_tactics_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.583001", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_groups (\n\tid SERIAL NOT NULL, \n\tgroup_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(2000), \n\tversion_id INTEGER, \n\taliases JSON, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\textra_data JSON, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_groups PRIMARY KEY (id), \n\tCONSTRAINT uix_group_version UNIQUE (group_id, version_id), \n\tCONSTRAINT fk_mitre_groups_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.604013", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.020536, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_groups (\n\tid SERIAL NOT NULL, \n\tgroup_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(2000), \n\tversion_id INTEGER, \n\taliases JSON, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\textra_data JSON, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_groups PRIMARY KEY (id), \n\tCONSTRAINT uix_group_version UNIQUE (group_id, version_id), \n\tCONSTRAINT fk_mitre_groups_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.609298", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_software (\n\tid SERIAL NOT NULL, \n\tsoftware_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(2000), \n\tversion_id INTEGER, \n\tsoftware_type VARCHAR(50), \n\tplatforms JSON, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\textra_data JSON, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_software PRIMARY KEY (id), \n\tCONSTRAINT uix_software_version UNIQUE (software_id, version_id), \n\tCONSTRAINT fk_mitre_software_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.631346", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.021674, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_software (\n\tid SERIAL NOT NULL, \n\tsoftware_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(2000), \n\tversion_id INTEGER, \n\tsoftware_type VARCHAR(50), \n\tplatforms JSON, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\textra_data JSON, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_software PRIMARY KEY (id), \n\tCONSTRAINT uix_software_version UNIQUE (software_id, version_id), \n\tCONSTRAINT fk_mitre_software_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.636514", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_mitigations (\n\tid SERIAL NOT NULL, \n\tmitigation_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(2000), \n\tversion_id INTEGER, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\textra_data JSON, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_mitigations PRIMARY KEY (id), \n\tCONSTRAINT uix_mitigation_version UNIQUE (mitigation_id, version_id), \n\tCONSTRAINT fk_mitre_mitigations_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.657698", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.019732, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_mitigations (\n\tid SERIAL NOT NULL, \n\tmitigation_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription VARCHAR(2000), \n\tversion_id INTEGER, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\textra_data JSON, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tCONSTRAINT pk_mitre_mitigations PRIMARY KEY (id), \n\tCONSTRAINT uix_mitigation_version UNIQUE (mitigation_id, version_id), \n\tCONSTRAINT fk_mitre_mitigations_version_id_mitre_versions FOREIGN KEY(version_id) REFERENCES mitre_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.662167", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_tactics (\n\tid SERIAL NOT NULL, \n\texternal_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(100) NOT NULL, \n\tdescription TEXT, \n\tversion_id INTEGER NOT NULL, \n\tcreated TIMESTAMP WITHOUT TIME ZONE, \n\tmodified TIMESTAMP WITHOUT TIME ZONE, \n\trevoked BOOLEAN NOT NULL, \n\tdeprecated BOOLEAN NOT NULL, \n\tCONSTRAINT pk_atlas_tactics PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_tactics_version_id_atlas_versions FOREIGN KEY(version_id) REFERENCES atlas_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.678640", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015546, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_tactics (\n\tid SERIAL NOT NULL, \n\texternal_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(100) NOT NULL, \n\tdescription TEXT, \n\tversion_id INTEGER NOT NULL, \n\tcreated TIMESTAMP WITHOUT TIME ZONE, \n\tmodified TIMESTAMP WITHOUT TIME ZONE, \n\trevoked BOOLEAN NOT NULL, \n\tdeprecated BOOLEAN NOT NULL, \n\tCONSTRAINT pk_atlas_tactics PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_tactics_version_id_atlas_versions FOREIGN KEY(version_id) REFERENCES atlas_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.683034", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_matrices (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tversion_id INTEGER NOT NULL, \n\tCONSTRAINT pk_atlas_matrices PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_matrices_version_id_atlas_versions FOREIGN KEY(version_id) REFERENCES atlas_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.698491", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015127, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_matrices (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tversion_id INTEGER NOT NULL, \n\tCONSTRAINT pk_atlas_matrices PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_matrices_version_id_atlas_versions FOREIGN KEY(version_id) REFERENCES atlas_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.703173", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE environments (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\ttype environmenttype NOT NULL, \n\tstatus environmentstatus NOT NULL, \n\tcreated_by VARCHAR NOT NULL, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN NOT NULL, \n\tis_revoked BOOLEAN NOT NULL, \n\trevoked_by_id VARCHAR, \n\tversion INTEGER NOT NULL, \n\tCONSTRAINT pk_environments PRIMARY KEY (id), \n\tCONSTRAINT fk_environments_created_by_users FOREIGN KEY(created_by) REFERENCES users (id), \n\tCONSTRAINT fk_environments_revoked_by_id_users FOREIGN KEY(revoked_by_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.719116", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015598, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE environments (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\ttype environmenttype NOT NULL, \n\tstatus environmentstatus NOT NULL, \n\tcreated_by VARCHAR NOT NULL, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN NOT NULL, \n\tis_revoked BOOLEAN NOT NULL, \n\trevoked_by_id VARCHAR, \n\tversion INTEGER NOT NULL, \n\tCONSTRAINT pk_environments PRIMARY KEY (id), \n\tCONSTRAINT fk_environments_created_by_users FOREIGN KEY(created_by) REFERENCES users (id), \n\tCONSTRAINT fk_environments_revoked_by_id_users FOREIGN KEY(revoked_by_id) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.723305", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_environments_id ON environments (id)"}
{"timestamp": "2025-04-01T16:35:29.733495", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009862, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_environments_id ON environments (id)"}
{"timestamp": "2025-04-01T16:35:29.737728", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_class_hierarchy (\n\tid SERIAL NOT NULL, \n\tsubclass_id INTEGER, \n\tsuperclass_id INTEGER, \n\tCONSTRAINT pk_d3f_class_hierarchy PRIMARY KEY (id), \n\tCONSTRAINT fk_d3f_class_hierarchy_subclass_id_d3f_classes FOREIGN KEY(subclass_id) REFERENCES d3f_classes (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_d3f_class_hierarchy_superclass_id_d3f_classes FOREIGN KEY(superclass_id) REFERENCES d3f_classes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.747745", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009563, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_class_hierarchy (\n\tid SERIAL NOT NULL, \n\tsubclass_id INTEGER, \n\tsuperclass_id INTEGER, \n\tCONSTRAINT pk_d3f_class_hierarchy PRIMARY KEY (id), \n\tCONSTRAINT fk_d3f_class_hierarchy_subclass_id_d3f_classes FOREIGN KEY(subclass_id) REFERENCES d3f_classes (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_d3f_class_hierarchy_superclass_id_d3f_classes FOREIGN KEY(superclass_id) REFERENCES d3f_classes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.752232", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3fend_concepts (\n\tid SERIAL NOT NULL, \n\turi VARCHAR(255) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\ttype VARCHAR(50) NOT NULL, \n\tdefinition TEXT, \n\tversion_id INTEGER NOT NULL, \n\tcreated TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tmodified TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tis_deprecated BOOLEAN NOT NULL, \n\texternal_references TEXT, \n\tnotes TEXT, \n\tCONSTRAINT pk_d3fend_concepts PRIMARY KEY (id), \n\tCONSTRAINT uq_d3fend_concepts_uri UNIQUE (uri), \n\tCONSTRAINT fk_d3fend_concepts_version_id_d3fend_versions FOREIGN KEY(version_id) REFERENCES d3fend_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.773680", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.020913, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3fend_concepts (\n\tid SERIAL NOT NULL, \n\turi VARCHAR(255) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\ttype VARCHAR(50) NOT NULL, \n\tdefinition TEXT, \n\tversion_id INTEGER NOT NULL, \n\tcreated TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tmodified TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tis_deprecated BOOLEAN NOT NULL, \n\texternal_references TEXT, \n\tnotes TEXT, \n\tCONSTRAINT pk_d3fend_concepts PRIMARY KEY (id), \n\tCONSTRAINT uq_d3fend_concepts_uri UNIQUE (uri), \n\tCONSTRAINT fk_d3fend_concepts_version_id_d3fend_versions FOREIGN KEY(version_id) REFERENCES d3fend_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.780381", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_class_property_relationships (\n\tid SERIAL NOT NULL, \n\tsource_class_id INTEGER, \n\tproperty_id INTEGER, \n\ttarget_class_id INTEGER, \n\tCONSTRAINT pk_d3f_class_property_relationships PRIMARY KEY (id), \n\tCONSTRAINT fk_d3f_class_property_relationships_source_class_id_d3f_classes FOREIGN KEY(source_class_id) REFERENCES d3f_classes (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_d3f_class_property_relationships_property_id_d3f_properties FOREIGN KEY(property_id) REFERENCES d3f_properties (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_d3f_class_property_relationships_target_class_id_d3f_classes FOREIGN KEY(target_class_id) REFERENCES d3f_classes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.791362", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.012151, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_class_property_relationships (\n\tid SERIAL NOT NULL, \n\tsource_class_id INTEGER, \n\tproperty_id INTEGER, \n\ttarget_class_id INTEGER, \n\tCONSTRAINT pk_d3f_class_property_relationships PRIMARY KEY (id), \n\tCONSTRAINT fk_d3f_class_property_relationships_source_class_id_d3f_classes FOREIGN KEY(source_class_id) REFERENCES d3f_classes (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_d3f_class_property_relationships_property_id_d3f_properties FOREIGN KEY(property_id) REFERENCES d3f_properties (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_d3f_class_property_relationships_target_class_id_d3f_classes FOREIGN KEY(target_class_id) REFERENCES d3f_classes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.796223", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_digital_artifacts (\n\tid SERIAL NOT NULL, \n\tclass_id INTEGER NOT NULL, \n\tartifact_name VARCHAR(255) NOT NULL, \n\tCONSTRAINT pk_d3f_digital_artifacts PRIMARY KEY (id), \n\tCONSTRAINT fk_d3f_digital_artifacts_class_id_d3f_classes FOREIGN KEY(class_id) REFERENCES d3f_classes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.808203", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.011224, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_digital_artifacts (\n\tid SERIAL NOT NULL, \n\tclass_id INTEGER NOT NULL, \n\tartifact_name VARCHAR(255) NOT NULL, \n\tCONSTRAINT pk_d3f_digital_artifacts PRIMARY KEY (id), \n\tCONSTRAINT fk_d3f_digital_artifacts_class_id_d3f_classes FOREIGN KEY(class_id) REFERENCES d3f_classes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.813203", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_countermeasures (\n\tid SERIAL NOT NULL, \n\tclass_id INTEGER NOT NULL, \n\tcountermeasure_name VARCHAR(255) NOT NULL, \n\timplementation_level VARCHAR(50), \n\tCONSTRAINT pk_d3f_countermeasures PRIMARY KEY (id), \n\tCONSTRAINT fk_d3f_countermeasures_class_id_d3f_classes FOREIGN KEY(class_id) REFERENCES d3f_classes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.823254", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.010083, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3f_countermeasures (\n\tid SERIAL NOT NULL, \n\tclass_id INTEGER NOT NULL, \n\tcountermeasure_name VARCHAR(255) NOT NULL, \n\timplementation_level VARCHAR(50), \n\tCONSTRAINT pk_d3f_countermeasures PRIMARY KEY (id), \n\tCONSTRAINT fk_d3f_countermeasures_class_id_d3f_classes FOREIGN KEY(class_id) REFERENCES d3f_classes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.828128", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_controls (\n\tid SERIAL NOT NULL, \n\texternal_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tversion_id INTEGER NOT NULL, \n\tcreated TIMESTAMP WITHOUT TIME ZONE, \n\tmodified TIMESTAMP WITHOUT TIME ZONE, \n\trevoked BOOLEAN, \n\tdeprecated BOOLEAN, \n\tCONSTRAINT pk_mitre_controls PRIMARY KEY (id), \n\tCONSTRAINT fk_mitre_controls_version_id_mitre_defense_versions FOREIGN KEY(version_id) REFERENCES mitre_defense_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.844233", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015709, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_controls (\n\tid SERIAL NOT NULL, \n\texternal_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tversion_id INTEGER NOT NULL, \n\tcreated TIMESTAMP WITHOUT TIME ZONE, \n\tmodified TIMESTAMP WITHOUT TIME ZONE, \n\trevoked BOOLEAN, \n\tdeprecated BOOLEAN, \n\tCONSTRAINT pk_mitre_controls PRIMARY KEY (id), \n\tCONSTRAINT fk_mitre_controls_version_id_mitre_defense_versions FOREIGN KEY(version_id) REFERENCES mitre_defense_versions (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.849204", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE testcase_chain_nodes (\n\tid SERIAL NOT NULL, \n\tchain_id INTEGER NOT NULL, \n\ttestcase_id INTEGER NOT NULL, \n\tnode_type VARCHAR DEFAULT 'standard' NOT NULL, \n\tposition_x FLOAT NOT NULL, \n\tposition_y FLOAT NOT NULL, \n\texecution_order INTEGER NOT NULL, \n\tcondition_expression VARCHAR, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_testcase_chain_nodes PRIMARY KEY (id), \n\tCONSTRAINT fk_testcase_chain_nodes_chain_id_testcase_chains FOREIGN KEY(chain_id) REFERENCES testcase_chains (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_testcase_chain_nodes_testcase_id_test_cases FOREIGN KEY(testcase_id) REFERENCES test_cases (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.865529", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015997, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE testcase_chain_nodes (\n\tid SERIAL NOT NULL, \n\tchain_id INTEGER NOT NULL, \n\ttestcase_id INTEGER NOT NULL, \n\tnode_type VARCHAR DEFAULT 'standard' NOT NULL, \n\tposition_x FLOAT NOT NULL, \n\tposition_y FLOAT NOT NULL, \n\texecution_order INTEGER NOT NULL, \n\tcondition_expression VARCHAR, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_testcase_chain_nodes PRIMARY KEY (id), \n\tCONSTRAINT fk_testcase_chain_nodes_chain_id_testcase_chains FOREIGN KEY(chain_id) REFERENCES testcase_chains (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_testcase_chain_nodes_testcase_id_test_cases FOREIGN KEY(testcase_id) REFERENCES test_cases (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.869787", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_testcase_chain_nodes_id ON testcase_chain_nodes (id)"}
{"timestamp": "2025-04-01T16:35:29.879917", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009873, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_testcase_chain_nodes_id ON testcase_chain_nodes (id)"}
{"timestamp": "2025-04-01T16:35:29.884308", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE chain_executions (\n\tid SERIAL NOT NULL, \n\tchain_id INTEGER NOT NULL, \n\tstarted_by VARCHAR NOT NULL, \n\tstart_time TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tend_time TIMESTAMP WITHOUT TIME ZONE, \n\tstatus VARCHAR DEFAULT 'running' NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_chain_executions PRIMARY KEY (id), \n\tCONSTRAINT fk_chain_executions_chain_id_testcase_chains FOREIGN KEY(chain_id) REFERENCES testcase_chains (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_chain_executions_started_by_users FOREIGN KEY(started_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.900262", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015433, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE chain_executions (\n\tid SERIAL NOT NULL, \n\tchain_id INTEGER NOT NULL, \n\tstarted_by VARCHAR NOT NULL, \n\tstart_time TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tend_time TIMESTAMP WITHOUT TIME ZONE, \n\tstatus VARCHAR DEFAULT 'running' NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_chain_executions PRIMARY KEY (id), \n\tCONSTRAINT fk_chain_executions_chain_id_testcase_chains FOREIGN KEY(chain_id) REFERENCES testcase_chains (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_chain_executions_started_by_users FOREIGN KEY(started_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.904528", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_chain_executions_id ON chain_executions (id)"}
{"timestamp": "2025-04-01T16:35:29.913764", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.008897, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_chain_executions_id ON chain_executions (id)"}
{"timestamp": "2025-04-01T16:35:29.918274", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE testcase_conditions (\n\tid SERIAL NOT NULL, \n\ttestcase_id INTEGER NOT NULL, \n\tcondition_type VARCHAR NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tvalidation_script VARCHAR, \n\trequired BOOLEAN NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_testcase_conditions PRIMARY KEY (id), \n\tCONSTRAINT fk_testcase_conditions_testcase_id_test_cases_base FOREIGN KEY(testcase_id) REFERENCES test_cases_base (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.933996", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015373, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE testcase_conditions (\n\tid SERIAL NOT NULL, \n\ttestcase_id INTEGER NOT NULL, \n\tcondition_type VARCHAR NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\tvalidation_script VARCHAR, \n\trequired BOOLEAN NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_testcase_conditions PRIMARY KEY (id), \n\tCONSTRAINT fk_testcase_conditions_testcase_id_test_cases_base FOREIGN KEY(testcase_id) REFERENCES test_cases_base (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.938041", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_testcase_conditions_id ON testcase_conditions (id)"}
{"timestamp": "2025-04-01T16:35:29.948003", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009574, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_testcase_conditions_id ON testcase_conditions (id)"}
{"timestamp": "2025-04-01T16:35:29.952389", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE assessments (\n\tid SERIAL NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\ttarget_system VARCHAR NOT NULL, \n\tassessment_type VARCHAR NOT NULL, \n\tstart_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tend_date TIMESTAMP WITHOUT TIME ZONE, \n\tstatus VARCHAR DEFAULT 'pending' NOT NULL, \n\tenvironment_id INTEGER, \n\tcreated_by VARCHAR NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_assessments PRIMARY KEY (id), \n\tCONSTRAINT fk_assessments_environment_id_environments FOREIGN KEY(environment_id) REFERENCES environments (id), \n\tCONSTRAINT fk_assessments_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.969893", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.016352, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE assessments (\n\tid SERIAL NOT NULL, \n\tname VARCHAR NOT NULL, \n\tdescription VARCHAR, \n\ttarget_system VARCHAR NOT NULL, \n\tassessment_type VARCHAR NOT NULL, \n\tstart_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tend_date TIMESTAMP WITHOUT TIME ZONE, \n\tstatus VARCHAR DEFAULT 'pending' NOT NULL, \n\tenvironment_id INTEGER, \n\tcreated_by VARCHAR NOT NULL, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_assessments PRIMARY KEY (id), \n\tCONSTRAINT fk_assessments_environment_id_environments FOREIGN KEY(environment_id) REFERENCES environments (id), \n\tCONSTRAINT fk_assessments_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.974074", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_assessments_id ON assessments (id)"}
{"timestamp": "2025-04-01T16:35:29.984144", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009305, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_assessments_id ON assessments (id)"}
{"timestamp": "2025-04-01T16:35:29.988482", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE group_technique_association (\n\tgroup_id INTEGER, \n\ttechnique_id INTEGER, \n\tCONSTRAINT fk_group_technique_association_group_id_mitre_groups FOREIGN KEY(group_id) REFERENCES mitre_groups (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_group_technique_association_technique_id_mitre_techniques FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.993268", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.004601, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE group_technique_association (\n\tgroup_id INTEGER, \n\ttechnique_id INTEGER, \n\tCONSTRAINT fk_group_technique_association_group_id_mitre_groups FOREIGN KEY(group_id) REFERENCES mitre_groups (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_group_technique_association_technique_id_mitre_techniques FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:29.997842", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE software_technique_association (\n\tsoftware_id INTEGER, \n\ttechnique_id INTEGER, \n\tCONSTRAINT fk_software_technique_association_software_id_mitre_software FOREIGN KEY(software_id) REFERENCES mitre_software (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_software_technique_association_technique_id_mitre_techniques FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.002805", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.004904, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE software_technique_association (\n\tsoftware_id INTEGER, \n\ttechnique_id INTEGER, \n\tCONSTRAINT fk_software_technique_association_software_id_mitre_software FOREIGN KEY(software_id) REFERENCES mitre_software (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_software_technique_association_technique_id_mitre_techniques FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.007556", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitigation_technique_association (\n\tmitigation_id INTEGER, \n\ttechnique_id INTEGER, \n\tCONSTRAINT fk_mitigation_technique_association_mitigation_id_mitre_5633 FOREIGN KEY(mitigation_id) REFERENCES mitre_mitigations (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_mitigation_technique_association_technique_id_mitre__9ed8 FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.012511", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.004763, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitigation_technique_association (\n\tmitigation_id INTEGER, \n\ttechnique_id INTEGER, \n\tCONSTRAINT fk_mitigation_technique_association_mitigation_id_mitre_5633 FOREIGN KEY(mitigation_id) REFERENCES mitre_mitigations (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_mitigation_technique_association_technique_id_mitre__9ed8 FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.017033", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_relationships (\n\tid SERIAL NOT NULL, \n\trelationship_id VARCHAR NOT NULL, \n\tsource_id INTEGER, \n\ttarget_id INTEGER, \n\trelationship_type VARCHAR NOT NULL, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tversion VARCHAR(10), \n\tCONSTRAINT pk_mitre_relationships PRIMARY KEY (id), \n\tCONSTRAINT fk_mitre_relationships_source_id_mitre_techniques FOREIGN KEY(source_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_mitre_relationships_target_id_mitre_techniques FOREIGN KEY(target_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.033131", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015483, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_relationships (\n\tid SERIAL NOT NULL, \n\trelationship_id VARCHAR NOT NULL, \n\tsource_id INTEGER, \n\ttarget_id INTEGER, \n\trelationship_type VARCHAR NOT NULL, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tversion VARCHAR(10), \n\tCONSTRAINT pk_mitre_relationships PRIMARY KEY (id), \n\tCONSTRAINT fk_mitre_relationships_source_id_mitre_techniques FOREIGN KEY(source_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_mitre_relationships_target_id_mitre_techniques FOREIGN KEY(target_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.037811", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE technique_tactic_association (\n\ttechnique_id INTEGER, \n\ttactic_id INTEGER, \n\tCONSTRAINT fk_technique_tactic_association_technique_id_mitre_techniques FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_technique_tactic_association_tactic_id_mitre_tactics FOREIGN KEY(tactic_id) REFERENCES mitre_tactics (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.042731", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.004868, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE technique_tactic_association (\n\ttechnique_id INTEGER, \n\ttactic_id INTEGER, \n\tCONSTRAINT fk_technique_tactic_association_technique_id_mitre_techniques FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_technique_tactic_association_tactic_id_mitre_tactics FOREIGN KEY(tactic_id) REFERENCES mitre_tactics (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.047243", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE technique_scores (\n\tid SERIAL NOT NULL, \n\ttechnique_id INTEGER NOT NULL, \n\tcategory scorecategory NOT NULL, \n\tscore FLOAT NOT NULL, \n\tweight FLOAT, \n\tnotes TEXT, \n\tcreated_by VARCHAR, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_technique_scores PRIMARY KEY (id), \n\tCONSTRAINT uix_technique_score_category UNIQUE (technique_id, category), \n\tCONSTRAINT fk_technique_scores_technique_id_mitre_techniques FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_technique_scores_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.069061", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.021099, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE technique_scores (\n\tid SERIAL NOT NULL, \n\ttechnique_id INTEGER NOT NULL, \n\tcategory scorecategory NOT NULL, \n\tscore FLOAT NOT NULL, \n\tweight FLOAT, \n\tnotes TEXT, \n\tcreated_by VARCHAR, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_technique_scores PRIMARY KEY (id), \n\tCONSTRAINT uix_technique_score_category UNIQUE (technique_id, category), \n\tCONSTRAINT fk_technique_scores_technique_id_mitre_techniques FOREIGN KEY(technique_id) REFERENCES mitre_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_technique_scores_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.073561", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_techniques (\n\tid SERIAL NOT NULL, \n\texternal_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tversion_id INTEGER NOT NULL, \n\ttactic_id INTEGER, \n\tcreated TIMESTAMP WITHOUT TIME ZONE, \n\tmodified TIMESTAMP WITHOUT TIME ZONE, \n\trevoked BOOLEAN NOT NULL, \n\tdeprecated BOOLEAN NOT NULL, \n\tis_subtechnique BOOLEAN NOT NULL, \n\tparent_technique_id INTEGER, \n\tCONSTRAINT pk_atlas_techniques PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_techniques_version_id_atlas_versions FOREIGN KEY(version_id) REFERENCES atlas_versions (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_atlas_techniques_tactic_id_atlas_tactics FOREIGN KEY(tactic_id) REFERENCES atlas_tactics (id) ON DELETE SET NULL, \n\tCONSTRAINT fk_atlas_techniques_parent_technique_id_atlas_techniques FOREIGN KEY(parent_technique_id) REFERENCES atlas_techniques (id) ON DELETE SET NULL\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.089664", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.01552, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_techniques (\n\tid SERIAL NOT NULL, \n\texternal_id VARCHAR(50) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tversion_id INTEGER NOT NULL, \n\ttactic_id INTEGER, \n\tcreated TIMESTAMP WITHOUT TIME ZONE, \n\tmodified TIMESTAMP WITHOUT TIME ZONE, \n\trevoked BOOLEAN NOT NULL, \n\tdeprecated BOOLEAN NOT NULL, \n\tis_subtechnique BOOLEAN NOT NULL, \n\tparent_technique_id INTEGER, \n\tCONSTRAINT pk_atlas_techniques PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_techniques_version_id_atlas_versions FOREIGN KEY(version_id) REFERENCES atlas_versions (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_atlas_techniques_tactic_id_atlas_tactics FOREIGN KEY(tactic_id) REFERENCES atlas_tactics (id) ON DELETE SET NULL, \n\tCONSTRAINT fk_atlas_techniques_parent_technique_id_atlas_techniques FOREIGN KEY(parent_technique_id) REFERENCES atlas_techniques (id) ON DELETE SET NULL\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.094239", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3fend_relationships (\n\tsource_id INTEGER NOT NULL, \n\ttarget_id INTEGER NOT NULL, \n\trelationship_type VARCHAR(50) NOT NULL, \n\tCONSTRAINT pk_d3fend_relationships PRIMARY KEY (source_id, target_id), \n\tCONSTRAINT fk_d3fend_relationships_source_id_d3fend_concepts FOREIGN KEY(source_id) REFERENCES d3fend_concepts (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_d3fend_relationships_target_id_d3fend_concepts FOREIGN KEY(target_id) REFERENCES d3fend_concepts (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.105924", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009608, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE d3fend_relationships (\n\tsource_id INTEGER NOT NULL, \n\ttarget_id INTEGER NOT NULL, \n\trelationship_type VARCHAR(50) NOT NULL, \n\tCONSTRAINT pk_d3fend_relationships PRIMARY KEY (source_id, target_id), \n\tCONSTRAINT fk_d3fend_relationships_source_id_d3fend_concepts FOREIGN KEY(source_id) REFERENCES d3fend_concepts (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_d3fend_relationships_target_id_d3fend_concepts FOREIGN KEY(target_id) REFERENCES d3fend_concepts (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.111399", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_control_relationships (\n\tid SERIAL NOT NULL, \n\tcontrol_id INTEGER NOT NULL, \n\ttarget_type VARCHAR(50) NOT NULL, \n\ttarget_id VARCHAR(255) NOT NULL, \n\trelationship_type VARCHAR(50) NOT NULL, \n\tdescription TEXT, \n\tCONSTRAINT pk_mitre_control_relationships PRIMARY KEY (id), \n\tCONSTRAINT fk_mitre_control_relationships_control_id_mitre_controls FOREIGN KEY(control_id) REFERENCES mitre_controls (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.127197", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015563, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE mitre_control_relationships (\n\tid SERIAL NOT NULL, \n\tcontrol_id INTEGER NOT NULL, \n\ttarget_type VARCHAR(50) NOT NULL, \n\ttarget_id VARCHAR(255) NOT NULL, \n\trelationship_type VARCHAR(50) NOT NULL, \n\tdescription TEXT, \n\tCONSTRAINT pk_mitre_control_relationships PRIMARY KEY (id), \n\tCONSTRAINT fk_mitre_control_relationships_control_id_mitre_controls FOREIGN KEY(control_id) REFERENCES mitre_controls (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.132442", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE testcase_chain_edges (\n\tid SERIAL NOT NULL, \n\tsource_node_id INTEGER NOT NULL, \n\ttarget_node_id INTEGER NOT NULL, \n\tedge_type VARCHAR DEFAULT 'standard' NOT NULL, \n\tcondition VARCHAR, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_testcase_chain_edges PRIMARY KEY (id), \n\tCONSTRAINT fk_testcase_chain_edges_source_node_id_testcase_chain_nodes FOREIGN KEY(source_node_id) REFERENCES testcase_chain_nodes (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_testcase_chain_edges_target_node_id_testcase_chain_nodes FOREIGN KEY(target_node_id) REFERENCES testcase_chain_nodes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.153317", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.019245, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE testcase_chain_edges (\n\tid SERIAL NOT NULL, \n\tsource_node_id INTEGER NOT NULL, \n\ttarget_node_id INTEGER NOT NULL, \n\tedge_type VARCHAR DEFAULT 'standard' NOT NULL, \n\tcondition VARCHAR, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_testcase_chain_edges PRIMARY KEY (id), \n\tCONSTRAINT fk_testcase_chain_edges_source_node_id_testcase_chain_nodes FOREIGN KEY(source_node_id) REFERENCES testcase_chain_nodes (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_testcase_chain_edges_target_node_id_testcase_chain_nodes FOREIGN KEY(target_node_id) REFERENCES testcase_chain_nodes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.158557", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_testcase_chain_edges_id ON testcase_chain_edges (id)"}
{"timestamp": "2025-04-01T16:35:30.170072", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.010962, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_testcase_chain_edges_id ON testcase_chain_edges (id)"}
{"timestamp": "2025-04-01T16:35:30.174690", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE node_executions (\n\tid SERIAL NOT NULL, \n\tchain_execution_id INTEGER NOT NULL, \n\tnode_id INTEGER NOT NULL, \n\tstart_time TIMESTAMP WITHOUT TIME ZONE, \n\tend_time TIMESTAMP WITHOUT TIME ZONE, \n\tstatus VARCHAR DEFAULT 'pending' NOT NULL, \n\tresult_data JSONB, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_node_executions PRIMARY KEY (id), \n\tCONSTRAINT fk_node_executions_chain_execution_id_chain_executions FOREIGN KEY(chain_execution_id) REFERENCES chain_executions (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_node_executions_node_id_testcase_chain_nodes FOREIGN KEY(node_id) REFERENCES testcase_chain_nodes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.191194", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.016055, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE node_executions (\n\tid SERIAL NOT NULL, \n\tchain_execution_id INTEGER NOT NULL, \n\tnode_id INTEGER NOT NULL, \n\tstart_time TIMESTAMP WITHOUT TIME ZONE, \n\tend_time TIMESTAMP WITHOUT TIME ZONE, \n\tstatus VARCHAR DEFAULT 'pending' NOT NULL, \n\tresult_data JSONB, \n\tcreated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tupdated_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL, \n\tdeleted_time TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_node_executions PRIMARY KEY (id), \n\tCONSTRAINT fk_node_executions_chain_execution_id_chain_executions FOREIGN KEY(chain_execution_id) REFERENCES chain_executions (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_node_executions_node_id_testcase_chain_nodes FOREIGN KEY(node_id) REFERENCES testcase_chain_nodes (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.195937", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_node_executions_id ON node_executions (id)"}
{"timestamp": "2025-04-01T16:35:30.205545", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009492, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_node_executions_id ON node_executions (id)"}
{"timestamp": "2025-04-01T16:35:30.210299", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE assessment_to_campaign (\n\tassessment_id INTEGER NOT NULL, \n\tcampaign_id INTEGER NOT NULL, \n\tCONSTRAINT pk_assessment_to_campaign PRIMARY KEY (assessment_id, campaign_id), \n\tCONSTRAINT fk_assessment_to_campaign_assessment_id_assessments FOREIGN KEY(assessment_id) REFERENCES assessments (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_assessment_to_campaign_campaign_id_cl_campaign FOREIGN KEY(campaign_id) REFERENCES cl_campaign (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.221593", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.01107, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE assessment_to_campaign (\n\tassessment_id INTEGER NOT NULL, \n\tcampaign_id INTEGER NOT NULL, \n\tCONSTRAINT pk_assessment_to_campaign PRIMARY KEY (assessment_id, campaign_id), \n\tCONSTRAINT fk_assessment_to_campaign_assessment_id_assessments FOREIGN KEY(assessment_id) REFERENCES assessments (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_assessment_to_campaign_campaign_id_cl_campaign FOREIGN KEY(campaign_id) REFERENCES cl_campaign (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.226204", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE test_executions (\n\tid SERIAL NOT NULL, \n\ttest_case_id INTEGER, \n\tassessment_id INTEGER, \n\tresult VARCHAR, \n\tnotes TEXT, \n\tevidence JSON, \n\texecuted_by VARCHAR, \n\texecuted_at TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_test_executions PRIMARY KEY (id), \n\tCONSTRAINT fk_test_executions_test_case_id_test_cases FOREIGN KEY(test_case_id) REFERENCES test_cases (id), \n\tCONSTRAINT fk_test_executions_assessment_id_assessments FOREIGN KEY(assessment_id) REFERENCES assessments (id), \n\tCONSTRAINT fk_test_executions_executed_by_users FOREIGN KEY(executed_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.242657", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015945, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE test_executions (\n\tid SERIAL NOT NULL, \n\ttest_case_id INTEGER, \n\tassessment_id INTEGER, \n\tresult VARCHAR, \n\tnotes TEXT, \n\tevidence JSON, \n\texecuted_by VARCHAR, \n\texecuted_at TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_test_executions PRIMARY KEY (id), \n\tCONSTRAINT fk_test_executions_test_case_id_test_cases FOREIGN KEY(test_case_id) REFERENCES test_cases (id), \n\tCONSTRAINT fk_test_executions_assessment_id_assessments FOREIGN KEY(assessment_id) REFERENCES assessments (id), \n\tCONSTRAINT fk_test_executions_executed_by_users FOREIGN KEY(executed_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.247034", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_test_executions_id ON test_executions (id)"}
{"timestamp": "2025-04-01T16:35:30.257001", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009166, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_test_executions_id ON test_executions (id)"}
{"timestamp": "2025-04-01T16:35:30.264127", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE campaigns (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tstatus VARCHAR(50) NOT NULL, \n\tstart_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tend_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tassessment_id INTEGER, \n\tcreated_by VARCHAR, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deleted BOOLEAN, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tversion VARCHAR(10), \n\tCONSTRAINT pk_campaigns PRIMARY KEY (id), \n\tCONSTRAINT fk_campaigns_assessment_id_assessments FOREIGN KEY(assessment_id) REFERENCES assessments (id), \n\tCONSTRAINT fk_campaigns_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.280760", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.018277, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE campaigns (\n\tid SERIAL NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tstatus VARCHAR(50) NOT NULL, \n\tstart_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tend_date TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tassessment_id INTEGER, \n\tcreated_by VARCHAR, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tis_deleted BOOLEAN, \n\tdeleted_at TIMESTAMP WITHOUT TIME ZONE, \n\tcreated_on TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tis_deprecated BOOLEAN, \n\tis_revoked BOOLEAN, \n\trevoked_by_id VARCHAR, \n\tversion VARCHAR(10), \n\tCONSTRAINT pk_campaigns PRIMARY KEY (id), \n\tCONSTRAINT fk_campaigns_assessment_id_assessments FOREIGN KEY(assessment_id) REFERENCES assessments (id), \n\tCONSTRAINT fk_campaigns_created_by_users FOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.285008", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_campaigns_id ON campaigns (id)"}
{"timestamp": "2025-04-01T16:35:30.294947", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009824, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_campaigns_id ON campaigns (id)"}
{"timestamp": "2025-04-01T16:35:30.299425", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_matrix_items (\n\tid SERIAL NOT NULL, \n\tmatrix_id INTEGER NOT NULL, \n\ttechnique_id INTEGER NOT NULL, \n\ttactic_id INTEGER NOT NULL, \n\tcolor VARCHAR(50), \n\tshow_subtechniques BOOLEAN NOT NULL, \n\tCONSTRAINT pk_atlas_matrix_items PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_matrix_items_matrix_id_atlas_matrices FOREIGN KEY(matrix_id) REFERENCES atlas_matrices (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_atlas_matrix_items_technique_id_atlas_techniques FOREIGN KEY(technique_id) REFERENCES atlas_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_atlas_matrix_items_tactic_id_atlas_tactics FOREIGN KEY(tactic_id) REFERENCES atlas_tactics (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.309269", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009683, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_matrix_items (\n\tid SERIAL NOT NULL, \n\tmatrix_id INTEGER NOT NULL, \n\ttechnique_id INTEGER NOT NULL, \n\ttactic_id INTEGER NOT NULL, \n\tcolor VARCHAR(50), \n\tshow_subtechniques BOOLEAN NOT NULL, \n\tCONSTRAINT pk_atlas_matrix_items PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_matrix_items_matrix_id_atlas_matrices FOREIGN KEY(matrix_id) REFERENCES atlas_matrices (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_atlas_matrix_items_technique_id_atlas_techniques FOREIGN KEY(technique_id) REFERENCES atlas_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_atlas_matrix_items_tactic_id_atlas_tactics FOREIGN KEY(tactic_id) REFERENCES atlas_tactics (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.314418", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_relationships (\n\tid SERIAL NOT NULL, \n\tsource_id INTEGER NOT NULL, \n\ttarget_id INTEGER NOT NULL, \n\ttype VARCHAR(50) NOT NULL, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tCONSTRAINT pk_atlas_relationships PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_relationships_source_id_atlas_techniques FOREIGN KEY(source_id) REFERENCES atlas_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_atlas_relationships_target_id_atlas_techniques FOREIGN KEY(target_id) REFERENCES atlas_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.325093", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.011111, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE atlas_relationships (\n\tid SERIAL NOT NULL, \n\tsource_id INTEGER NOT NULL, \n\ttarget_id INTEGER NOT NULL, \n\ttype VARCHAR(50) NOT NULL, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL, \n\tCONSTRAINT pk_atlas_relationships PRIMARY KEY (id), \n\tCONSTRAINT fk_atlas_relationships_source_id_atlas_techniques FOREIGN KEY(source_id) REFERENCES atlas_techniques (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_atlas_relationships_target_id_atlas_techniques FOREIGN KEY(target_id) REFERENCES atlas_techniques (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.329510", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE campaign_test_cases (\n\tid SERIAL NOT NULL, \n\tcampaign_id INTEGER NOT NULL, \n\ttest_case_id INTEGER NOT NULL, \n\tstatus VARCHAR(50) NOT NULL, \n\tassigned_to VARCHAR, \n\tnotes TEXT, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_campaign_test_cases PRIMARY KEY (id), \n\tCONSTRAINT fk_campaign_test_cases_campaign_id_campaigns FOREIGN KEY(campaign_id) REFERENCES campaigns (id), \n\tCONSTRAINT fk_campaign_test_cases_test_case_id_test_cases FOREIGN KEY(test_case_id) REFERENCES test_cases (id), \n\tCONSTRAINT fk_campaign_test_cases_assigned_to_users FOREIGN KEY(assigned_to) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.345385", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.015309, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE campaign_test_cases (\n\tid SERIAL NOT NULL, \n\tcampaign_id INTEGER NOT NULL, \n\ttest_case_id INTEGER NOT NULL, \n\tstatus VARCHAR(50) NOT NULL, \n\tassigned_to VARCHAR, \n\tnotes TEXT, \n\tcreated_at TIMESTAMP WITHOUT TIME ZONE, \n\tupdated_at TIMESTAMP WITHOUT TIME ZONE, \n\tCONSTRAINT pk_campaign_test_cases PRIMARY KEY (id), \n\tCONSTRAINT fk_campaign_test_cases_campaign_id_campaigns FOREIGN KEY(campaign_id) REFERENCES campaigns (id), \n\tCONSTRAINT fk_campaign_test_cases_test_case_id_test_cases FOREIGN KEY(test_case_id) REFERENCES test_cases (id), \n\tCONSTRAINT fk_campaign_test_cases_assigned_to_users FOREIGN KEY(assigned_to) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.350205", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_campaign_test_cases_id ON campaign_test_cases (id)"}
{"timestamp": "2025-04-01T16:35:30.359832", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.009436, "executemany": false, "parameters": {}, "statement": "CREATE INDEX ix_campaign_test_cases_id ON campaign_test_cases (id)"}
{"timestamp": "2025-04-01T16:35:30.364751", "level": "DEBUG", "module": "database", "function": "before_cursor_execute", "line": 60, "message": "Query starting", "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE campaign_to_organization (\n\tcampaign_id INTEGER, \n\torganization_id INTEGER, \n\tCONSTRAINT fk_campaign_to_organization_campaign_id_campaigns FOREIGN KEY(campaign_id) REFERENCES campaigns (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_campaign_to_organization_organization_id_organizations FOREIGN KEY(organization_id) REFERENCES organizations (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.370054", "level": "DEBUG", "module": "database", "function": "after_cursor_execute", "line": 73, "message": "Query completed", "duration": 0.005297, "executemany": false, "parameters": {}, "statement": "\nCREATE TABLE campaign_to_organization (\n\tcampaign_id INTEGER, \n\torganization_id INTEGER, \n\tCONSTRAINT fk_campaign_to_organization_campaign_id_campaigns FOREIGN KEY(campaign_id) REFERENCES campaigns (id) ON DELETE CASCADE, \n\tCONSTRAINT fk_campaign_to_organization_organization_id_organizations FOREIGN KEY(organization_id) REFERENCES organizations (id) ON DELETE CASCADE\n)\n\n"}
{"timestamp": "2025-04-01T16:35:30.377934", "level": "INFO", "module": "database", "function": "init_db", "line": 149, "message": "Database tables created successfully"}
{"timestamp": "2025-04-01T16:35:30.383141", "level": "INFO", "module": "conftest", "function": "clear_db", "line": 40, "message": "Successfully reset database"}
