{"timestamp": "2025-04-01T16:35:02.208169", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.217074", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.225064", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:02.938945", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:02.947419", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.956857", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.964435", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:08.532414", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:08.541374", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:08.549228", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/testcase-chains/"}
{"timestamp": "2025-04-01T16:35:08.557271", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:11.776451", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: invalid-id"}
{"timestamp": "2025-04-01T16:35:11.795243", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Too many techniques. Maximum allowed is 50"}
{"timestamp": "2025-04-01T16:35:11.811809", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID type: <class 'int'>"}
{"timestamp": "2025-04-01T16:35:11.829688", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: T1566' OR '1'='1"}
{"timestamp": "2025-04-01T16:35:12.595655", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:12.604762", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:12.613717", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:12.622162", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:13.436324", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:13.445340", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:13.454344", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:13.462901", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:14.285154", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:14.294160", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:14.303217", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:14.311812", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:15.152569", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:15.162938", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:15.172020", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:15.180583", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.045083", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.054914", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.066103", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.077117", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.853853", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.863930", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.872905", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.881487", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.734340", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:17.744876", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:17.755160", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:17.764971", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.953464", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.021264", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.030937", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.040872", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.050667", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:18.494007", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.561807", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.571256", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.581171", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.591411", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.005959", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.074640", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.084730", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.094449", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/1/restore"}
{"timestamp": "2025-04-01T16:35:20.104219", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.262792", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.327935", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.338411", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.347982", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/campaigns/"}
{"timestamp": "2025-04-01T16:35:20.357621", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.412420", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.422446", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.432278", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/campaigns/1/restore"}
{"timestamp": "2025-04-01T16:35:20.442736", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.487192", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.496923", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.506579", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.515165", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.553893", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.563172", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.572942", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/9999"}
{"timestamp": "2025-04-01T16:35:20.581617", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.622647", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.634079", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.644446", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.655261", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.695431", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.706032", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.715271", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.724937", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.766815", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.776598", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.786218", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.796214", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.836724", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.847411", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.857713", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.866764", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.908693", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.923857", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.934601", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.945240", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.990094", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:21.001197", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.013137", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:21.023808", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:21.198537", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.268229", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.279452", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.289977", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.299302", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:21.825655", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.890636", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.900287", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.909578", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.918565", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:22.950147", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.026950", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.036663", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.046690", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/1/restore"}
{"timestamp": "2025-04-01T16:35:23.055952", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:23.099051", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.111278", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.122089", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.132779", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.173603", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.183143", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.192854", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/9999"}
{"timestamp": "2025-04-01T16:35:23.202240", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.246159", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.255661", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.265219", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.274190", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.315592", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.327459", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.339193", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.351702", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.394638", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.405634", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.418613", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.429233", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.476254", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.487658", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.497830", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.509052", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.553420", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.563066", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.572796", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.582138", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.626975", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.638676", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.648735", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.658779", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.704650", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.714708", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.724411", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.733472", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.775885", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.785552", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.794664", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.803297", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.848944", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.861398", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.871774", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.882130", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.922383", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.932128", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.941377", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/999"}
{"timestamp": "2025-04-01T16:35:23.950633", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.990089", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.999112", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:24.008008", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/non-existent-endpoint"}
{"timestamp": "2025-04-01T16:35:24.016654", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.260250", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.268993", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.277567", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.285864", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.323667", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.332668", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.342212", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.351893", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.386099", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.395455", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.404997", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.414248", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.461547", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.471433", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.481312", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.491690", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.536535", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.546790", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.557319", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.566303", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.603225", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.612151", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.620879", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.629993", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.667843", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.676902", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.685637", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.693831", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.731931", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.740962", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.749468", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.758242", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.801939", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.813153", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.823236", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.831704", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.873336", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.883579", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.894385", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/bulk"}
{"timestamp": "2025-04-01T16:35:25.904399", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.947962", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.957152", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.965646", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.973823", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.008264", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.018113", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.028620", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/stats"}
{"timestamp": "2025-04-01T16:35:26.038445", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.095547", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.111745", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.126153", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:26.139967", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
