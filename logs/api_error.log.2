{"timestamp": "2025-04-01T16:35:02.207966", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.216999", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.224981", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:02.938866", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:02.947334", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.956779", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.964360", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:08.532331", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:08.541287", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:08.549156", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/testcase-chains/"}
{"timestamp": "2025-04-01T16:35:08.557188", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:11.776367", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: invalid-id"}
{"timestamp": "2025-04-01T16:35:11.795165", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Too many techniques. Maximum allowed is 50"}
{"timestamp": "2025-04-01T16:35:11.811731", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID type: <class 'int'>"}
{"timestamp": "2025-04-01T16:35:11.829605", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: T1566' OR '1'='1"}
{"timestamp": "2025-04-01T16:35:12.595579", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:12.604680", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:12.613641", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:12.622084", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:13.436246", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:13.445262", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:13.454261", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:13.462823", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:14.285075", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:14.294082", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:14.303138", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:14.311734", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:15.152420", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:15.162857", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:15.171943", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:15.180508", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.045002", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.054828", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.066012", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.077037", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.853776", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.863851", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.872829", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.881411", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.734253", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:17.744790", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:17.755079", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:17.764882", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.951882", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.021188", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.030850", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.040785", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.050561", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:18.492356", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.561730", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.571157", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.581087", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.591331", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.004363", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.074550", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.084638", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.094372", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/1/restore"}
{"timestamp": "2025-04-01T16:35:20.104139", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.261267", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.327856", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.338319", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.347905", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/campaigns/"}
{"timestamp": "2025-04-01T16:35:20.357544", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.412335", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.422360", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.432195", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/campaigns/1/restore"}
{"timestamp": "2025-04-01T16:35:20.442649", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.487093", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.496847", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.506503", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.515091", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.553813", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.563094", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.572867", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/9999"}
{"timestamp": "2025-04-01T16:35:20.581541", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.622570", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.633997", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.644366", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.655178", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.695349", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.705947", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.715193", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.724853", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.766735", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.776520", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.786140", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.796137", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.836645", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.847314", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.857628", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.866678", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.908612", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.923762", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.934519", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.945158", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.989976", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:21.001115", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.013044", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:21.023712", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:21.196973", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.268138", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.279333", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.289899", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.299216", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:21.824117", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.890537", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.900208", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.909497", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.918486", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:22.948400", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.026867", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.036565", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.046606", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/1/restore"}
{"timestamp": "2025-04-01T16:35:23.055875", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:23.098939", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.111190", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.122009", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.132697", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.173525", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.183065", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.192768", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/9999"}
{"timestamp": "2025-04-01T16:35:23.202163", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.246080", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.255582", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.265141", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.274113", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.315508", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.327363", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.339102", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.351620", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.394546", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.405523", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.418526", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.429154", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.476166", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.487575", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.497746", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.508960", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.553336", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.562988", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.572719", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.582057", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.626893", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.638594", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.648654", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.658702", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.704570", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.714625", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.724328", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.733371", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.775810", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.785472", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.794590", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.803223", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.848858", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.861311", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.871695", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.882039", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.922301", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.932049", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.941286", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/999"}
{"timestamp": "2025-04-01T16:35:23.950554", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.990010", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.999032", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:24.007930", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/non-existent-endpoint"}
{"timestamp": "2025-04-01T16:35:24.016577", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.260174", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.268914", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.277492", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.285790", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.323592", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.332592", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.342130", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.351809", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.386022", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.395373", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.404919", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.414169", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.461468", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.471352", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.481230", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.491584", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.536457", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.546708", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.557242", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.566228", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.603149", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.612076", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.620796", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.629919", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.667762", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.676829", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.685566", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.693756", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.731858", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.740889", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.749395", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.758161", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.801848", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.813059", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.823163", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.831630", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.873256", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.883466", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.894304", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/bulk"}
{"timestamp": "2025-04-01T16:35:25.904307", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.947890", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.957076", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.965574", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.973753", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.008177", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.018036", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.028527", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/stats"}
{"timestamp": "2025-04-01T16:35:26.038347", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.095300", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.111612", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.126029", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:26.139842", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
