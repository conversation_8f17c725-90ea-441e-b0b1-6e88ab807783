{"timestamp": "2025-04-01T16:35:02.207759", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.216925", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.224896", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:02.938786", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:02.947256", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.956704", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.964284", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:08.532252", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:08.541208", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:08.549084", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/testcase-chains/"}
{"timestamp": "2025-04-01T16:35:08.557104", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:11.776279", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: invalid-id"}
{"timestamp": "2025-04-01T16:35:11.795087", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Too many techniques. Maximum allowed is 50"}
{"timestamp": "2025-04-01T16:35:11.811653", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID type: <class 'int'>"}
{"timestamp": "2025-04-01T16:35:11.829521", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: T1566' OR '1'='1"}
{"timestamp": "2025-04-01T16:35:12.595502", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:12.604600", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:12.613566", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:12.622008", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:13.436169", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:13.445184", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:13.454179", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:13.462747", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:14.284998", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:14.294003", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:14.303060", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:14.311659", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:15.152277", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:15.162770", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:15.171866", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:15.180433", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.044919", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.054743", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.065928", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.076958", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.853697", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.863773", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.872755", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.881330", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.734163", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:17.744701", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:17.754998", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:17.764793", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.950308", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.021109", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.030766", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.040697", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.050340", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:18.490765", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.561651", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.571066", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.581003", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.591257", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.002785", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.074459", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.084545", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.094290", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/1/restore"}
{"timestamp": "2025-04-01T16:35:20.104038", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.259092", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.327775", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.338240", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.347830", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/campaigns/"}
{"timestamp": "2025-04-01T16:35:20.357467", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.412257", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.422268", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.432113", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/campaigns/1/restore"}
{"timestamp": "2025-04-01T16:35:20.442563", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.487015", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.496769", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.506426", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.515017", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.553734", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.563017", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.572793", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/9999"}
{"timestamp": "2025-04-01T16:35:20.581466", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.622492", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.633916", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.644284", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.655096", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.695266", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.705865", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.715115", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.724768", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.766655", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.776442", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.786062", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.796061", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.836565", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.847229", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.857542", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.866592", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.908531", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.923652", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.934440", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.945077", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.989898", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:21.001034", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.012953", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:21.023618", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:21.195414", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.268054", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.279232", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.289798", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.299136", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:21.822577", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.890458", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.900129", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.909418", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.918408", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:22.946649", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.026786", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.036322", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.046523", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/1/restore"}
{"timestamp": "2025-04-01T16:35:23.055798", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:23.098828", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.111103", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.121916", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.132616", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.173448", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.182987", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.192689", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/9999"}
{"timestamp": "2025-04-01T16:35:23.202088", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.246002", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.255504", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.265063", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.274037", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.315422", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.327247", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.338971", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.351522", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.394441", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.405441", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.418428", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.429075", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.476068", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.487493", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.497665", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.508874", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.553259", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.562910", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.572643", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.581978", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.626811", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.638495", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.648570", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.658626", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.704472", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.714542", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.724250", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.733281", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.775734", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.785390", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.794515", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.803149", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.848751", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.861198", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.871603", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.881939", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.922223", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.931969", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.941200", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/999"}
{"timestamp": "2025-04-01T16:35:23.950475", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.989932", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.998954", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:24.007855", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/non-existent-endpoint"}
{"timestamp": "2025-04-01T16:35:24.016499", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.260097", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.268836", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.277416", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.285716", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.323517", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.332516", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.342046", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.351726", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.385944", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.395289", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.404841", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.414089", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.461387", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.471248", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.481143", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.491503", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.536379", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.546627", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.557165", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.566154", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.603075", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.612000", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.620713", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.629844", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.667680", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.676756", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.685494", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.693682", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.731785", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.740815", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.749318", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.758074", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.801768", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.812978", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.823089", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.831556", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.873176", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.883284", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.894225", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/bulk"}
{"timestamp": "2025-04-01T16:35:25.904220", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.947818", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.957001", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.965502", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.973682", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.008098", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.017960", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.028434", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/stats"}
{"timestamp": "2025-04-01T16:35:26.038258", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.095210", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.111481", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.125900", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:26.139717", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
