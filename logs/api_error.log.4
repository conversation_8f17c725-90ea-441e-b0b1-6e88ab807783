{"timestamp": "2025-04-01T16:35:02.207549", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.216850", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.224816", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:02.938704", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:02.947176", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.956627", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.964209", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:08.532170", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:08.541129", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:08.549012", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/testcase-chains/"}
{"timestamp": "2025-04-01T16:35:08.557020", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:11.776196", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: invalid-id"}
{"timestamp": "2025-04-01T16:35:11.795010", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Too many techniques. Maximum allowed is 50"}
{"timestamp": "2025-04-01T16:35:11.811575", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID type: <class 'int'>"}
{"timestamp": "2025-04-01T16:35:11.829437", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: T1566' OR '1'='1"}
{"timestamp": "2025-04-01T16:35:12.595425", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:12.604519", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:12.613489", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:12.621930", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:13.436090", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:13.445106", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:13.454097", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:13.462670", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:14.284921", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:14.293924", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:14.302982", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:14.311582", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:15.152175", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:15.162679", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:15.171786", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:15.180356", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.044837", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.054657", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.065841", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.076880", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.853612", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.863694", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.872679", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.881254", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.734077", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:17.744618", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:17.754918", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:17.764713", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.948736", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.021032", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.030686", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.040611", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.050262", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:18.489185", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.561573", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.570986", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.580919", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.591183", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.001222", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.074345", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.084450", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.094213", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/1/restore"}
{"timestamp": "2025-04-01T16:35:20.103952", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.257550", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.327695", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.338162", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.347756", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/campaigns/"}
{"timestamp": "2025-04-01T16:35:20.357388", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.412179", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.422172", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.432033", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/campaigns/1/restore"}
{"timestamp": "2025-04-01T16:35:20.442475", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.486938", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.496692", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.506346", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.514944", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.553655", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.562940", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.572717", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/9999"}
{"timestamp": "2025-04-01T16:35:20.581391", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.622416", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.633835", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.644205", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.654993", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.695184", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.705776", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.715037", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.724683", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.766576", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.776364", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.785985", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.795985", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.836484", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.847147", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.857456", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.866504", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.908448", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.923548", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.934360", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.944997", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.989817", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:21.000953", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.012861", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:21.023520", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:21.193851", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.267960", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.279138", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.289714", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.299057", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:21.821041", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.890377", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.900045", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.909336", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.918325", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:22.944899", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.026705", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.036241", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.046438", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/1/restore"}
{"timestamp": "2025-04-01T16:35:23.055720", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:23.098716", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.111017", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.121825", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.132536", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.173369", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.182909", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.192611", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/9999"}
{"timestamp": "2025-04-01T16:35:23.202011", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.245923", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.255425", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.264986", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.273959", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.315335", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.327152", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.338838", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.351267", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.394276", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.405355", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.418344", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.428997", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.475980", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.487409", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.497583", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.508784", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.553181", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.562831", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.572565", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.581896", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.626731", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.638395", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.648480", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.658547", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.704389", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.714459", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.724173", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.733198", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.775657", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.785309", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.794441", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.803076", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.848649", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.861118", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.871510", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.881860", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.922145", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.931890", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.941113", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/999"}
{"timestamp": "2025-04-01T16:35:23.950395", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.989854", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.998874", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:24.007776", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/non-existent-endpoint"}
{"timestamp": "2025-04-01T16:35:24.016417", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.260021", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.268760", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.277336", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.285641", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.323442", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.332440", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.341964", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.351643", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.385865", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.395211", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.404761", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.414008", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.461303", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.471167", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.481060", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.491422", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.536297", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.546547", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.557087", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.566078", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.603000", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.611924", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.620630", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.629769", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.667598", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.676683", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.685424", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.693607", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.731714", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.740742", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.749246", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.758001", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.801686", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.812873", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.823016", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.831481", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.873096", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.883201", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.894145", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/bulk"}
{"timestamp": "2025-04-01T16:35:25.904134", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.947744", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.956926", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.965431", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.973611", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.008006", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.017883", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.028353", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/stats"}
{"timestamp": "2025-04-01T16:35:26.038171", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.095115", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.111350", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.125770", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:26.139613", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
