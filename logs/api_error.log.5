{"timestamp": "2025-04-01T16:35:02.207343", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.216773", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.224735", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:02.938623", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:02.947094", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:02.956550", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:02.964135", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:08.532091", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:08.541048", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:08.548937", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/testcase-chains/"}
{"timestamp": "2025-04-01T16:35:08.556928", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:11.776111", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: invalid-id"}
{"timestamp": "2025-04-01T16:35:11.794927", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Too many techniques. Maximum allowed is 50"}
{"timestamp": "2025-04-01T16:35:11.811491", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID type: <class 'int'>"}
{"timestamp": "2025-04-01T16:35:11.829350", "level": "ERROR", "module": "mapper", "function": "get_attack_path_coverage", "line": 207, "message": "Error in get_attack_path_coverage: Invalid technique ID format: T1566' OR '1'='1"}
{"timestamp": "2025-04-01T16:35:12.595342", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:12.604436", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:12.613412", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:12.621852", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:13.436011", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:13.445028", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:13.454016", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:13.462592", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:14.284838", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:14.293847", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:14.302902", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:14.311504", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:15.152070", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:15.162592", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:15.171708", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:15.180276", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.044756", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.054568", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.065755", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.076799", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:16.853529", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:16.863611", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:16.872603", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:16.881178", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.733985", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:17.744532", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:17.754835", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/users/"}
{"timestamp": "2025-04-01T16:35:17.764632", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:17.947146", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.020951", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.030605", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.040521", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.050174", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:18.487609", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.561494", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.570904", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:18.580833", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/"}
{"timestamp": "2025-04-01T16:35:18.591109", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:19.999657", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.074241", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.084317", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.094136", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/assessments/1/restore"}
{"timestamp": "2025-04-01T16:35:20.103852", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.255861", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.327613", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.338081", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.347678", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/campaigns/"}
{"timestamp": "2025-04-01T16:35:20.357301", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:20.412101", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.422087", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.431952", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/campaigns/1/restore"}
{"timestamp": "2025-04-01T16:35:20.442386", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.486856", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.496612", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.506265", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.514868", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.553573", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.562862", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.572641", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/9999"}
{"timestamp": "2025-04-01T16:35:20.581313", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.622335", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.633749", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.644124", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.654889", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.695101", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.705690", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.714961", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.724598", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.766494", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.776281", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.785905", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.795906", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.836404", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.847054", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.857361", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.866401", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.908366", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:20.923453", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:20.934274", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:20.944915", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:20.989735", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:21.000865", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.012761", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/tags/"}
{"timestamp": "2025-04-01T16:35:21.023413", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:21.192283", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.267880", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.279023", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.289631", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.298973", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:21.819486", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.890286", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.899924", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:21.909256", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/"}
{"timestamp": "2025-04-01T16:35:21.918246", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:22.943118", "level": "ERROR", "module": "database", "function": "get_db", "line": 115, "message": "Database session error", "error": "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path.", "exception": "Traceback (most recent call last):\n  File \"/app/api/database.py\", line 113, in get_db\n    yield db\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/concurrency.py\", line 27, in contextmanager_in_threadpool\n    yield await run_in_threadpool(cm.__enter__)\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/routing.py\", line 291, in app\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 615, in solve_dependencies\n    solved_result = await solve_dependencies(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/fastapi/dependencies/utils.py\", line 638, in solve_dependencies\n    solved = await call(**solved_result.values)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/app/api/auth/dependencies.py\", line 41, in get_current_user\n    user = db.query(User).filter(User.username == username).first()\n           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/session.py\", line 2955, in query\n    return self._query_cls(entities, self, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 276, in __init__\n    self._set_entities(entities)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/query.py\", line 289, in <listcomp>\n    coercions.expect(\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py\", line 388, in expect\n    insp._post_inspect\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py\", line 1257, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2724, in _post_inspect\n    self._check_configure()\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 2401, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4214, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File \"/usr/local/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py\", line 4251, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.026621", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 138, "message": "Database Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.036161", "level": "ERROR", "module": "error_handler", "function": "sqlalchemy_exception_handler", "line": 155, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.046353", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 500: /api/v1/environments/1/restore"}
{"timestamp": "2025-04-01T16:35:23.055641", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 500"}
{"timestamp": "2025-04-01T16:35:23.098603", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.110928", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.121737", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.132455", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.173279", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.182829", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.192529", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/9999"}
{"timestamp": "2025-04-01T16:35:23.201932", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.245844", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.255339", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.264908", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.273883", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.315225", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.327033", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.338742", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.351108", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.394193", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.405265", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.418252", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.428917", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.475894", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.487299", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.497499", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/error-handling/"}
{"timestamp": "2025-04-01T16:35:23.508689", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.553100", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.562750", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.572485", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.581812", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.626646", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.638256", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.648394", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.658467", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.704299", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.714371", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.724094", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/"}
{"timestamp": "2025-04-01T16:35:23.733116", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.775578", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.785230", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.794365", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.802999", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.848552", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.861034", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.871350", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/1"}
{"timestamp": "2025-04-01T16:35:23.881762", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.922065", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.931809", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:23.941035", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/errors/999"}
{"timestamp": "2025-04-01T16:35:23.950309", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:23.989771", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:23.998793", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:24.007688", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/non-existent-endpoint"}
{"timestamp": "2025-04-01T16:35:24.016309", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.259940", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.268681", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.277259", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.285564", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.323367", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.332363", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.341880", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.351560", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.385786", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.395130", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.404682", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.413926", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.461214", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.471084", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.480979", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.491328", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.536217", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.546461", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.557009", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.566001", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.602925", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.611848", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.620544", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.629694", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.667514", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.676609", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.685348", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.693532", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.731640", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.740667", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.749173", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.757912", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.801590", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.812784", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.822941", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.831406", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.873013", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.883116", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.894063", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/bulk"}
{"timestamp": "2025-04-01T16:35:25.904046", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:25.947672", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:25.956851", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:25.965358", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:25.973540", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.007922", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.017805", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.028262", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases/stats"}
{"timestamp": "2025-04-01T16:35:26.038080", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
{"timestamp": "2025-04-01T16:35:26.094980", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 82, "message": "HTTP Exception: Not Found"}
{"timestamp": "2025-04-01T16:35:26.111210", "level": "ERROR", "module": "error_handler", "function": "http_exception_handler", "line": 99, "message": "Failed to store error in database: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[TestcaseChainNodeDB(testcase_chain_nodes)]'. Original exception was: Multiple classes found for path \"TestCaseDB\" in the registry of this declarative base. Please use a fully module-qualified path."}
{"timestamp": "2025-04-01T16:35:26.125639", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 36, "message": "HTTP Error 404: /api/v1/test-cases"}
{"timestamp": "2025-04-01T16:35:26.139481", "level": "ERROR", "module": "error_handler", "function": "send_wrapper", "line": 39, "message": "Error status: 404"}
