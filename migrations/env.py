from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context

# Add your model's MetaData object here
from api.database import Base
from api.models.user import User
from api.models.user_preferences import UserPreference
from api.models.mitre import (
    MitreVersion, MitreTechnique, MitreTactic,
    MitreGroup, MitreSoftware, MitreMitigation
)
from api.models.mitre_defense import MitreDefenseVersion, MitreControl, MitreControlRelationship
import os

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Update the database URL from environment variable
config.set_main_option("sqlalchemy.url", os.environ["DATABASE_URL"])

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Add your model's MetaData object here for autogenerate support
target_metadata = Base.metadata

def include_object(object, name, type_, reflected, compare_to):
    """Filter function for migration generation."""
    # Exclude d3fend tables from migrations as they're managed separately
    if type_ == "table":
        return not name.startswith('d3fend_')
    return True

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_object=include_object,
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            include_object=include_object,
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()