"""Add admin and audit models.

Revision ID: 20240315_add_admin_and_audit_models
Revises: 20240315_add_user_activity
Create Date: 2024-03-15 13:00:00.000000
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic
revision = '20240315_add_admin_and_audit_models'
down_revision = '20240315_add_user_activity'
branch_labels = None
depends_on = None

def upgrade():
    """Add admin and audit models."""
    # Create audit_logs table
    op.create_table(
        'audit_logs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=True),
        sa.Column('action', sa.String(), nullable=False),
        sa.Column('resource_type', sa.String(), nullable=False),
        sa.Column('resource_id', sa.String(), nullable=True),
        sa.Column('details', postgresql.JSONB(), nullable=True),
        sa.Column('ip_address', sa.String(), nullable=True),
        sa.Column('user_agent', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_audit_logs_user_id_users', ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id', name='pk_audit_logs')
    )

    # Create admin_audit_logs table
    op.create_table(
        'admin_audit_logs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=True),
        sa.Column('action', sa.String(), nullable=False),
        sa.Column('resource_type', sa.String(), nullable=False),
        sa.Column('resource_id', sa.String(), nullable=True),
        sa.Column('details', postgresql.JSONB(), nullable=True),
        sa.Column('ip_address', sa.String(), nullable=True),
        sa.Column('user_agent', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_admin_audit_logs_user_id_users', ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id', name='pk_admin_audit_logs')
    )

    # Create admin_notifications table
    op.create_table(
        'admin_notifications',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('message', sa.String(), nullable=False),
        sa.Column('level', sa.String(), nullable=False),
        sa.Column('metadata', postgresql.JSONB(), nullable=True),
        sa.Column('read', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('read_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_admin_notifications_user_id_users', ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id', name='pk_admin_notifications')
    )

    # Create error_logs table
    op.create_table(
        'error_logs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=True),
        sa.Column('error_type', sa.String(), nullable=False),
        sa.Column('error_message', sa.String(), nullable=False),
        sa.Column('stack_trace', sa.String(), nullable=True),
        sa.Column('metadata', postgresql.JSONB(), nullable=True),
        sa.Column('ip_address', sa.String(), nullable=True),
        sa.Column('user_agent', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_error_logs_user_id_users', ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id', name='pk_error_logs')
    )

    # Create indexes
    op.create_index('ix_audit_logs_user_id', 'audit_logs', ['user_id'])
    op.create_index('ix_audit_logs_created_at', 'audit_logs', ['created_at'])
    op.create_index('ix_admin_audit_logs_user_id', 'admin_audit_logs', ['user_id'])
    op.create_index('ix_admin_audit_logs_created_at', 'admin_audit_logs', ['created_at'])
    op.create_index('ix_admin_notifications_user_id', 'admin_notifications', ['user_id'])
    op.create_index('ix_admin_notifications_created_at', 'admin_notifications', ['created_at'])
    op.create_index('ix_error_logs_user_id', 'error_logs', ['user_id'])
    op.create_index('ix_error_logs_created_at', 'error_logs', ['created_at'])

def downgrade():
    """Remove admin and audit models."""
    # Drop indexes
    op.drop_index('ix_error_logs_created_at')
    op.drop_index('ix_error_logs_user_id')
    op.drop_index('ix_admin_notifications_created_at')
    op.drop_index('ix_admin_notifications_user_id')
    op.drop_index('ix_admin_audit_logs_created_at')
    op.drop_index('ix_admin_audit_logs_user_id')
    op.drop_index('ix_audit_logs_created_at')
    op.drop_index('ix_audit_logs_user_id')

    # Drop tables
    op.drop_table('error_logs')
    op.drop_table('admin_notifications')
    op.drop_table('admin_audit_logs')
    op.drop_table('audit_logs') 