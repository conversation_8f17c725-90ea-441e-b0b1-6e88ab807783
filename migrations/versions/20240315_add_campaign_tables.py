"""
Add campaign tables.

Revision ID: 20240315_add_campaign_tables
Revises: 20240314_add_assessment_and_test_execution_tables
Create Date: 2024-03-15 10:00:00.000000
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '20240315_add_campaign_tables'
down_revision = '20240314_add_assessment_and_test_execution_tables'
branch_labels = None
depends_on = None


def upgrade():
    # Create campaigns table
    op.create_table(
        'campaigns',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('start_date', sa.DateTime(), nullable=False),
        sa.Column('end_date', sa.DateTime(), nullable=False),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow, onupdate=datetime.utcnow),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), default=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_campaigns_id'), 'campaigns', ['id'], unique=False)
    
    # Create campaign_test_cases table
    op.create_table(
        'campaign_test_cases',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('campaign_id', sa.Integer(), nullable=False),
        sa.Column('test_case_id', sa.Integer(), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('assigned_to', sa.Integer(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow, onupdate=datetime.utcnow),
        sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ),
        sa.ForeignKeyConstraint(['test_case_id'], ['test_cases.id'], ),
        sa.ForeignKeyConstraint(['assigned_to'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_campaign_test_cases_id'), 'campaign_test_cases', ['id'], unique=False)
    
    # Add campaign_id foreign key to assessments table
    op.add_column('assessments', sa.Column('campaign_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'assessments', 'campaigns', ['campaign_id'], ['id'])


def downgrade():
    # Remove campaign_id foreign key from assessments table
    op.drop_constraint(None, 'assessments', type_='foreignkey')
    op.drop_column('assessments', 'campaign_id')
    
    # Drop campaign_test_cases table
    op.drop_index(op.f('ix_campaign_test_cases_id'), table_name='campaign_test_cases')
    op.drop_table('campaign_test_cases')
    
    # Drop campaigns table
    op.drop_index(op.f('ix_campaigns_id'), table_name='campaigns')
    op.drop_table('campaigns') 