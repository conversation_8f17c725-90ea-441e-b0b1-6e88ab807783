"""Add user activity table.

Revision ID: 20240315_add_user_activity
Revises: 20240315_fix_user_sessions
Create Date: 2024-03-15 12:00:00.000000
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic
revision = '20240315_add_user_activity'
down_revision = '20240315_fix_user_sessions'
branch_labels = None
depends_on = None

def upgrade():
    """Add user activity table."""
    op.create_table(
        'user_activities',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('activity_type', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('metadata', postgresql.JSONB(), nullable=True),
        sa.Column('ip_address', sa.String(), nullable=True),
        sa.Column('user_agent', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_user_activities_user_id_users'),
        sa.PrimaryKeyConstraint('id', name='pk_user_activities')
    )

    # Add indexes
    op.create_index('ix_user_activities_user_id', 'user_activities', ['user_id'])
    op.create_index('ix_user_activities_activity_type', 'user_activities', ['activity_type'])
    op.create_index('ix_user_activities_created_at', 'user_activities', ['created_at'])

def downgrade():
    """Remove user activity table."""
    op.drop_index('ix_user_activities_created_at')
    op.drop_index('ix_user_activities_activity_type')
    op.drop_index('ix_user_activities_user_id')
    op.drop_table('user_activities') 