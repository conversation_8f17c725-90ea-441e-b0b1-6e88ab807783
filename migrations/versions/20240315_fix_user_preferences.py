"""Fix user preferences table schema

Revision ID: 20240315_fix_user_preferences
Revises: 20240315_fix_user_sessions
Create Date: 2024-03-15 19:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20240315_fix_user_preferences'
down_revision = '20240315_fix_user_sessions'
branch_labels = None
depends_on = None

def upgrade():
    # Drop existing foreign key constraint
    op.drop_constraint('fk_user_preferences_user_id_users', 'user_preferences', type_='foreignkey')
    
    # Drop existing primary key constraint
    op.drop_constraint('pk_user_preferences', 'user_preferences', type_='primary')
    
    # Alter id column to use UUID
    op.alter_column('user_preferences', 'id',
        type_=sa.String(),
        postgresql_using='id::text',
        server_default=sa.text("gen_random_uuid()::text"),
        existing_nullable=False
    )
    
    # Alter user_id column to use UUID
    op.alter_column('user_preferences', 'user_id',
        type_=sa.String(),
        postgresql_using='user_id::text',
        existing_nullable=False
    )
    
    # Add timestamps
    op.add_column('user_preferences', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')))
    op.add_column('user_preferences', sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.text('now()')))
    
    # Recreate foreign key constraint
    op.create_foreign_key('fk_user_preferences_user_id_users', 'user_preferences', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    
    # Recreate primary key constraint
    op.create_primary_key('pk_user_preferences', 'user_preferences', ['id'])

def downgrade():
    # Drop constraints
    op.drop_constraint('fk_user_preferences_user_id_users', 'user_preferences', type_='foreignkey')
    op.drop_constraint('pk_user_preferences', 'user_preferences', type_='primary')
    
    # Drop timestamps
    op.drop_column('user_preferences', 'updated_at')
    op.drop_column('user_preferences', 'created_at')
    
    # Revert id and user_id columns to integer
    op.alter_column('user_preferences', 'id',
        type_=sa.Integer(),
        postgresql_using='id::integer',
        autoincrement=True,
        existing_nullable=False
    )
    
    op.alter_column('user_preferences', 'user_id',
        type_=sa.Integer(),
        postgresql_using='user_id::integer',
        existing_nullable=False
    )
    
    # Recreate constraints
    op.create_foreign_key('fk_user_preferences_user_id_users', 'user_preferences', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.create_primary_key('pk_user_preferences', 'user_preferences', ['id']) 