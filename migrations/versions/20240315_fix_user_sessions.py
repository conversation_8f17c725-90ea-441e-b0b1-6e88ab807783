"""Fix user sessions table schema

Revision ID: 20240315_fix_user_sessions
Revises: 20240315_add_campaign_tables
Create Date: 2024-03-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20240315_fix_user_sessions'
down_revision = '20240315_add_campaign_tables'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Drop existing user_sessions table if it exists
    op.drop_table('user_sessions')

    # Create device_info table
    op.create_table(
        'device_info',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_agent', sa.String(), nullable=False),
        sa.Column('ip_address', sa.String(), nullable=False),
        sa.Column('device_type', sa.String(), nullable=True),
        sa.Column('os_info', sa.String(), nullable=True),
        sa.Column('browser_info', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.Column('last_seen', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # Create new user_sessions table
    op.create_table(
        'user_sessions',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('device_id', sa.String(), nullable=True),
        sa.Column('session_token', sa.String(), nullable=False),
        sa.Column('is_active', sa.Boolean(), server_default='true'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.Column('last_activity', sa.DateTime(timezone=True), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('logout_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('ip_address', sa.String(), nullable=True),
        sa.Column('user_agent', sa.String(), nullable=True),
        sa.ForeignKeyConstraint(['device_id'], ['device_info.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )

    # Create indexes
    op.create_index('ix_user_sessions_session_token', 'user_sessions', ['session_token'], unique=True)
    op.create_index('ix_user_sessions_user_id', 'user_sessions', ['user_id'])
    op.create_index('ix_user_sessions_device_id', 'user_sessions', ['device_id'])

def downgrade() -> None:
    # Drop indexes
    op.drop_index('ix_user_sessions_device_id')
    op.drop_index('ix_user_sessions_user_id')
    op.drop_index('ix_user_sessions_session_token')

    # Drop tables
    op.drop_table('user_sessions')
    op.drop_table('device_info') 