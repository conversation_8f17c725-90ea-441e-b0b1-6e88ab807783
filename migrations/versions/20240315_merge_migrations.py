"""Merge migrations

Revision ID: 20240315_merge_migrations
Revises: 20240315_fix_user_preferences, 20240315_fix_user_sessions
Create Date: 2024-03-15 19:30:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20240315_merge_migrations'
down_revision = None
branch_labels = None
depends_on = None

# Multiple revisions being merged
revisions = ['20240315_fix_user_preferences', '20240315_fix_user_sessions']

def upgrade():
    pass

def downgrade():
    pass 