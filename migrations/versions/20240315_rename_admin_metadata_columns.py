"""Rename metadata columns in admin models

Revision ID: 20240315_rename_admin_metadata
Revises: 20240315_rename_metadata
Create Date: 2024-03-15 12:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20240315_rename_admin_metadata'
down_revision = '20240315_rename_metadata'
branch_labels = None
depends_on = None


def upgrade():
    # Rename metadata column in admin_notifications table
    op.alter_column('admin_notifications', 'metadata',
                    new_column_name='notification_metadata',
                    existing_type=postgresql.JSON(),
                    nullable=True)
    
    # Rename metadata column in error_logs table
    op.alter_column('error_logs', 'metadata',
                    new_column_name='error_metadata',
                    existing_type=postgresql.JSON(),
                    nullable=True)


def downgrade():
    # Rename notification_metadata column back to metadata
    op.alter_column('admin_notifications', 'notification_metadata',
                    new_column_name='metadata',
                    existing_type=postgresql.JSON(),
                    nullable=True)
    
    # Rename error_metadata column back to metadata
    op.alter_column('error_logs', 'error_metadata',
                    new_column_name='metadata',
                    existing_type=postgresql.JSON(),
                    nullable=True) 