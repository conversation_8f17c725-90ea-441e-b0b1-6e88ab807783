"""Rename metadata column to activity_metadata

Revision ID: 20240315_rename_metadata
Revises: 20240315_merge_migrations
Create Date: 2024-03-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20240315_rename_metadata'
down_revision = '20240315_merge_migrations'
branch_labels = None
depends_on = None


def upgrade():
    # Rename metadata column to activity_metadata
    op.alter_column('user_activities', 'metadata',
                    new_column_name='activity_metadata',
                    existing_type=postgresql.JSON(),
                    nullable=True)


def downgrade():
    # Rename activity_metadata column back to metadata
    op.alter_column('user_activities', 'activity_metadata',
                    new_column_name='metadata',
                    existing_type=postgresql.JSON(),
                    nullable=True) 