"""Merge all migration heads

Revision ID: 20240321_merge_all_heads
Revises: 20240315_add_admin_and_audit_models, 20240315_fix_user_preferences, 7fdd3ba9cc6a, 20240315_rename_admin_metadata, 20240315_merge_heads
Create Date: 2024-03-21 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20240321_merge_all_heads'
down_revision = None
branch_labels = None
depends_on = None

# Multiple heads being merged:
# - 20240315_add_admin_and_audit_models (add admin and audit models)
# - 20240315_fix_user_preferences (fix user preferences)
# - 7fdd3ba9cc6a (merge remove_circular and update_test_cases)
# - 20240315_rename_admin_metadata (rename metadata columns)
# - 20240315_merge_heads (previous merge)

def upgrade():
    pass


def downgrade():
    pass 