"""Merge all migration heads.

Revision ID: 20240331_merge_all_heads
Revises: 20240315_add_admin_and_audit_models, 20240315_fix_user_preferences, 20240315_merge_heads, 20240315_rename_admin_metadata, 20240321_merge_all_heads, 7fdd3ba9cc6a
Create Date: 2024-03-31 05:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20240331_merge_all_heads'
down_revision = ('20240315_add_admin_and_audit_models', '20240315_fix_user_preferences', '20240315_merge_heads', '20240315_rename_admin_metadata', '20240321_merge_all_heads', '7fdd3ba9cc6a')
branch_labels = None
depends_on = None

def upgrade() -> None:
    """Merge all heads."""
    pass

def downgrade() -> None:
    """Downgrade all heads."""
    pass 