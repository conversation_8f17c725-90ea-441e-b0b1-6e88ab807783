"""Add account unlock fields

Revision ID: 20250610_add_account_unlock_fields
Revises: 20240331_merge_all_heads
Create Date: 2025-06-10 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250610_add_account_unlock_fields'
down_revision = '20240331_merge_all_heads'
branch_labels = None
depends_on = None


def upgrade():
    """Add account unlock token fields to users table."""
    # Add account unlock token fields
    op.add_column('users', sa.Column('account_unlock_token', sa.String(256), nullable=True))
    op.add_column('users', sa.Column('account_unlock_expires', sa.DateTime(timezone=True), nullable=True))


def downgrade():
    """Remove account unlock token fields from users table."""
    # Remove account unlock token fields
    op.drop_column('users', 'account_unlock_expires')
    op.drop_column('users', 'account_unlock_token')
