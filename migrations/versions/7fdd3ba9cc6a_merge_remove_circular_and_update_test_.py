"""merge remove_circular and update_test_cases

Revision ID: 7fdd3ba9cc6a
Revises: 20240319_remove_circular, 20240320_update_test_cases_table
Create Date: 2025-03-30 15:48:51.860409

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7fdd3ba9cc6a'
down_revision = ('20240319_remove_circular', '20240320_update_test_cases_table')
branch_labels = None
depends_on = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
