"""
Models for comprehensive tagging system feature
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime

class TagBase(BaseModel):
    """Base tag model"""
    name: str = Field(..., description="Name of the tag", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the tag")
    color: str = Field("#3498db", description="Hex color code for the tag", pattern="^#[0-9A-Fa-f]{6}$")

class TagCreate(TagBase):
    """Tag creation model"""
    pass

class TagUpdate(BaseModel):
    """Tag update model"""
    name: Optional[str] = Field(None, description="Name of the tag", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the tag")
    color: Optional[str] = Field(None, description="Hex color code for the tag", pattern="^#[0-9A-Fa-f]{6}$")

class TagModel(TagBase):
    """Tag response model"""
    id: int = Field(..., description="Unique identifier for the tag")
    created_at: datetime = Field(..., description="Timestamp when the tag was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the tag was last updated")
    usage_count: int = Field(0, description="Number of entities using this tag")
    
    class Config:
        from_attributes = True

class TagAssociationBase(BaseModel):
    """Base tag association model"""
    tag_id: int = Field(..., description="ID of the tag")
    entity_type: str = Field(..., description="Type of the entity (e.g., 'test_case', 'campaign')")
    entity_id: int = Field(..., description="ID of the entity")

class TagAssociationCreate(TagAssociationBase):
    """Tag association creation model"""
    pass

class TagAssociationModel(TagAssociationBase):
    """Tag association response model"""
    id: int = Field(..., description="Unique identifier for the tag association")
    created_at: datetime = Field(..., description="Timestamp when the tag association was created")
    created_by_id: Optional[int] = Field(None, description="ID of the user who created the tag association")
    
    class Config:
        from_attributes = True

class EntityTags(BaseModel):
    """Model for adding/removing tags to/from an entity"""
    entity_type: str = Field(..., description="Type of the entity (e.g., 'test_case', 'campaign')")
    entity_id: int = Field(..., description="ID of the entity")
    tag_ids: List[int] = Field(..., description="List of tag IDs to add/remove")

class TagStats(BaseModel):
    """Model for tag usage statistics"""
    id: int = Field(..., description="Tag ID")
    name: str = Field(..., description="Tag name")
    color: str = Field(..., description="Tag color")
    count: int = Field(..., description="Number of entities using this tag")
    entity_types: Dict[str, int] = Field(..., description="Count by entity type")
