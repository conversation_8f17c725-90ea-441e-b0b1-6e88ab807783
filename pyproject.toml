[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "babel>=2.17.0",
    "email-validator>=2.2.0",
    "fastapi-limiter>=0.1.6",
    "fastapi>=0.115.8",
    "flask>=3.1.0",
    "flask-sqlalchemy>=3.1.1",
    "gunicorn>=23.0.0",
    "httpx>=0.28.1",
    "mitreattack-python>=3.0.8",
    "passlib[bcrypt]>=1.7.4",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.10.6",
    "pytest>=8.3.4",
    "pytest-asyncio>=0.25.3",
    "pytest-cov>=6.0.0",
    "python-jose[cryptography]>=3.4.0",
    "python-multipart>=0.0.20",
    "starlette>=0.45.3",
    "uvicorn>=0.34.0",
    "redis>=5.2.1",
    "sqlalchemy>=2.0.38",
    "sqlalchemy-utils>=0.41.2",
    "alembic>=1.14.1",
    "rdflib>=7.1.3",
    "interrogate>=1.7.0",
    "stix2>=3.0.1",
    "streamlit>=1.42.2",
    "networkx>=3.4.2",
    "plotly>=6.0.0",
    "requests>=2.32.3",
    "components>=0.0.1a0",
    "jinja2>=3.1.5",
    "selenium>=4.29.0",
    "pytest-selenium>=4.1.0",
    "webdriver-manager>=4.0.2",
    "trafilatura>=2.0.0",
    "playwright>=1.50.0",
    "starlette-admin>=0.14.1",
    "flask-login>=0.6.3",
    "flask-wtf>=1.2.2",
    "oauthlib>=3.2.2",
    "aiofiles>=24.1.0",
    "werkzeug>=3.1.3",
    "bcrypt>=4.2.1",
    "fastapi-mail>=1.4.2",
    "user-agents>=2.2.0",
    "ua-parser>=1.0.1",
    "wtforms>=3.2.1",
    "pyotp>=2.8.0",
    "qrcode>=7.4.2",
    "pillow>=10.0.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = """
    --verbose
    --cov=api
    --cov-report=term-missing
    --cov-report=html
    --cov-fail-under=80
"""

[tool.interrogate]
ignore-init-method = true
ignore-init-module = false
ignore-magic = false
ignore-semiprivate = false
ignore-private = false
ignore-property-decorators = false
ignore-module = false
ignore-nested-functions = false
ignore-nested-classes = false
ignore-setters = false
fail-under = 95
exclude = ["setup.py", "docs", "build", ".git", "tests"]
ignore-regex = ["^get$", "^mock_.*", ".*BaseClass.*"]
verbose = 2
quiet = false
whitelist-regex = []
color = true
omit-covered-files = false
generate-badge = "docs/interrogate_badge.svg"
badge-format = "svg"
badge-style = "flat"
