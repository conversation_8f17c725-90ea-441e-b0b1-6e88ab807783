<!-- Feature Documentation -->
<h2 id="feature-documentation">Feature Documentation</h2>
<p>This section contains detailed documentation about the features and capabilities of the system.</p>

<h3>Security Testing</h3>
<ul>
    <li><strong>Assessments:</strong> Create and manage security assessments for your systems.</li>
    <li><strong>Campaigns:</strong> Organize test cases into focused testing campaigns.</li>
    <li><strong>Test Cases:</strong> Define and track individual security test cases.</li>
</ul>

<!-- Technical Documentation -->
<h2 id="technical-documentation">Technical Documentation</h2>
<p>Technical details about the system architecture, APIs, and implementation.</p>

<h3>Architecture Overview</h3>
<div class="mermaid">
graph TD
    A[Web Interface] --> B[Flask Application]
    B --> C[Database]
    B --> D[API Layer]
    D --> E[Test Execution Engine]
    D --> F[Reporting Engine]
</div>

<!-- Planning and Roadmap -->
<h2 id="planning-and-roadmap">Planning & Roadmap</h2>
<p>Information about planned features and development roadmap.</p>

<h3>Current Development Focus</h3>
<ul>
    <li>Enhanced test case management</li>
    <li>Improved reporting capabilities</li>
    <li>Integration with additional security tools</li>
</ul>

<!-- Development Guides -->
<h2 id="development-guides">Development Guides</h2>
<p>Guidelines and best practices for developers working on the system.</p>

<h3>Getting Started</h3>
<ol>
    <li>Clone the repository</li>
    <li>Set up your development environment</li>
    <li>Run the application using Docker Compose</li>
</ol>

<h3>Code Standards</h3>
<pre><code># Python code style
- Follow PEP 8 guidelines
- Use type hints where possible
- Write comprehensive docstrings
- Include unit tests for new features</code></pre> 