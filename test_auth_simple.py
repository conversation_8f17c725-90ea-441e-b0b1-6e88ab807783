#!/usr/bin/env python3
"""Simple test script for authentication enhancements."""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all authentication modules can be imported."""
    try:
        from api.auth.router import router
        from api.auth.utils import validate_password, validate_email
        from api.models.user import User, UserRole
        print("✅ All authentication modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_password_validation():
    """Test password validation function."""
    try:
        from api.auth.utils import validate_password
        
        # Test valid password
        valid_password = "SecurePass123!"
        is_valid, message = validate_password(valid_password)
        assert is_valid, f"Valid password failed: {message}"
        
        # Test weak password
        weak_password = "weak"
        is_valid, message = validate_password(weak_password)
        assert not is_valid, "Weak password should fail validation"
        
        print("✅ Password validation tests passed")
        return True
    except Exception as e:
        print(f"❌ Password validation test failed: {e}")
        return False

def test_email_validation():
    """Test email validation function."""
    try:
        from api.auth.utils import validate_email
        
        # Test valid email
        valid_email = "<EMAIL>"
        is_valid = validate_email(valid_email)
        assert is_valid, "Valid email should pass validation"
        
        # Test invalid email
        invalid_email = "invalid-email"
        is_valid = validate_email(invalid_email)
        assert not is_valid, "Invalid email should fail validation"
        
        print("✅ Email validation tests passed")
        return True
    except Exception as e:
        print(f"❌ Email validation test failed: {e}")
        return False

def test_user_model():
    """Test User model functionality."""
    try:
        from api.models.user import User, UserRole
        
        # Create a user instance
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.VIEWER,
            full_name="Test User"
        )
        
        # Test password setting and verification
        password = "SecurePass123!"
        user.set_password(password)
        assert user.verify_password(password), "Password verification should work"
        assert not user.verify_password("wrongpassword"), "Wrong password should fail"
        
        print("✅ User model tests passed")
        return True
    except Exception as e:
        print(f"❌ User model test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Running Authentication Enhancement Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_password_validation,
        test_email_validation,
        test_user_model
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Authentication enhancements are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
