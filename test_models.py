#!/usr/bin/env python3
"""
Simple test script to validate model changes.
This script tests the model definitions without requiring the full test framework.
"""
from sqlalchemy import create_engine, MetaData, Column, Integer, String, DateTime, Boolean, ForeignKey, Table, Text, func
from sqlalchemy.orm import sessionmaker, declarative_base, relationship, Mapped, mapped_column
from sqlalchemy.sql import func
from typing import List, Optional
from datetime import datetime

# Create Base class
Base = declarative_base()

# Define a User class for foreign key relationships
class User(Base):
    __tablename__ = "users"
    id = Column(String, primary_key=True, default=lambda: "user-123")
    username = Column(String, unique=True, nullable=False)
    
# Define test versions of our models with the updated field types
class TestCaseDB(Base):
    """Database model for individual security test cases."""
    __tablename__ = "test_cases_base"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    expected_result = Column(String, nullable=False)
    actual_result = Column(String, nullable=True)
    status = Column(String, server_default="pending")
    created_by = Column(String, ForeignKey("users.id"), nullable=True)
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by])

class TestcaseChainDB(Base):
    """Database model for testcase chains."""
    __tablename__ = "testcase_chains"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    created_by = Column(String, ForeignKey("users.id"))  # Changed to String
    status = Column(String, server_default="draft")

    # Relationships
    creator = relationship("User", foreign_keys=[created_by])

class ChainExecutionDB(Base):
    """Database model for testcase chain executions."""
    __tablename__ = "chain_executions"

    id = Column(Integer, primary_key=True)
    chain_id = Column(Integer, ForeignKey("testcase_chains.id", ondelete="CASCADE"))
    started_by = Column(String, ForeignKey("users.id"))  # Changed to String
    start_time = Column(DateTime, default=func.now())
    end_time = Column(DateTime, nullable=True)
    status = Column(String, server_default="running")

    # Relationships
    chain = relationship("TestcaseChainDB")
    starter = relationship("User", foreign_keys=[started_by])

class TagAssociation(Base):
    """Association table for tags and their related entities"""
    __tablename__ = "tag_associations"
    
    id = Column(Integer, primary_key=True)
    tag_id = Column(Integer)
    entity_type = Column(String(50), nullable=False)
    entity_id = Column(String, nullable=False)  # Changed to String
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by_id = Column(String, ForeignKey("users.id"), nullable=True)  # Already String
    
    # Relationships
    created_by = relationship("User", foreign_keys=[created_by_id])

# Association table for tags and resources
tag_resource = Table(
    'tag_resource',
    Base.metadata,
    Column('tag_id', Integer, primary_key=True),
    Column('resource_type', String(50), primary_key=True),
    Column('resource_id', String, primary_key=True),  # Changed to String
    Column('created_by', String, ForeignKey('users.id')),  # Changed to String
    Column('created_at', DateTime, default=datetime.utcnow),
)

def main():
    """Main function to test model definitions."""
    # Create in-memory SQLite database for testing
    engine = create_engine('sqlite:///:memory:')
    
    # Create all tables
    Base.metadata.create_all(engine)
    
    # Create session
    Session = sessionmaker(bind=engine)
    session = Session()
    
    print("Successfully created all tables!")
    
    # Print table names
    inspector = MetaData()
    inspector.reflect(bind=engine)
    print("\nTables created:")
    for table in inspector.sorted_tables:
        print(f"- {table.name}")
    
    # Test creating objects to verify field types
    print("\nTesting model creation:")
    
    # Create a test user
    user = User(id="user-123", username="testuser")
    session.add(user)
    session.flush()
    
    # Test TestCaseDB
    test_case = TestCaseDB(
        name="Test Case",
        description="Test Description",
        expected_result="Expected Result",
        created_by="user-123"  # String format for User.id
    )
    session.add(test_case)
    
    # Test TestcaseChainDB
    chain = TestcaseChainDB(
        name="Test Chain",
        description="Test Chain Description",
        created_by="user-123"  # String format for User.id
    )
    session.add(chain)
    
    # Test ChainExecutionDB
    execution = ChainExecutionDB(
        chain_id=1,
        started_by="user-123"  # String format for User.id
    )
    session.add(execution)
    
    # Test TagAssociation
    tag_association = TagAssociation(
        tag_id=1,
        entity_type="user",
        entity_id="user-123",  # String format for User.id
        created_by_id="user-123"  # String format for User.id
    )
    session.add(tag_association)
    
    # Commit session to test constraints
    try:
        session.commit()
        print("Successfully created test objects!")
        
        # Verify the data types
        print("\nVerifying data types:")
        print(f"TestCaseDB.created_by: {type(test_case.created_by).__name__}")
        print(f"TestcaseChainDB.created_by: {type(chain.created_by).__name__}")
        print(f"ChainExecutionDB.started_by: {type(execution.started_by).__name__}")
        print(f"TagAssociation.entity_id: {type(tag_association.entity_id).__name__}")
        print(f"TagAssociation.created_by_id: {type(tag_association.created_by_id).__name__}")
        
    except Exception as e:
        session.rollback()
        print(f"Error creating test objects: {e}")
    
    # Close session
    session.close()

if __name__ == "__main__":
    main() 