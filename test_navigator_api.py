#!/usr/bin/env python3
"""
Simple test script for the enhanced MITRE Navigator API
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_navigator_config():
    """Test the navigator configuration endpoint."""
    try:
        # Import here to avoid import issues
        from api.services.mitre_navigator_enhanced import EnhancedNavigatorService
        
        # Create a mock database session for testing
        class MockDB:
            def query(self, model):
                return MockQuery()
        
        class MockQuery:
            def filter(self, *args):
                return self
            
            def join(self, *args):
                return self
            
            def all(self):
                return []
            
            def first(self):
                return None
        
        # Test the service
        service = EnhancedNavigatorService(MockDB())
        config = await service.get_navigator_config()
        
        print("✅ Navigator configuration test passed!")
        print(f"Domain: {config['domain']}")
        print(f"Version: {config['version']}")
        print(f"Theme: {config['custom_theme']['name']}")
        print(f"Default layers: {len(config['default_layers'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Navigator configuration test failed: {e}")
        return False

async def test_layer_generation():
    """Test layer generation."""
    try:
        from api.services.mitre_navigator_enhanced import EnhancedNavigatorService
        
        # Create a mock database session
        class MockDB:
            def query(self, model):
                return MockQuery()
        
        class MockQuery:
            def filter(self, *args):
                return self
            
            def join(self, *args):
                return self
            
            def all(self):
                # Return mock MITRE techniques
                if hasattr(self, '_model') and 'MitreTechnique' in str(self._model):
                    return [MockTechnique()]
                return []
            
            def first(self):
                return None
        
        class MockTechnique:
            def __init__(self):
                self.technique_id = "T1059.001"
                self.name = "PowerShell"
                self.description = "Adversaries may abuse PowerShell commands and scripts for execution."
                self.platform = "Windows"
                self.tactic = "Execution"
        
        # Test the service
        service = EnhancedNavigatorService(MockDB())
        layer = await service.generate_real_time_layer()
        
        print("✅ Layer generation test passed!")
        print(f"Layer name: {layer['name']}")
        print(f"Domain: {layer['domain']}")
        print(f"Techniques: {len(layer['techniques'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Layer generation test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🧪 Testing Enhanced MITRE Navigator API")
    print("=" * 50)
    
    tests = [
        ("Navigator Configuration", test_navigator_config),
        ("Layer Generation", test_layer_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        if await test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
