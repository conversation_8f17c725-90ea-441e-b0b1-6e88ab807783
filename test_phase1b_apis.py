#!/usr/bin/env python3
"""
Test script for Phase 1B API endpoints
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_navigator_websocket():
    """Test the Navigator WebSocket functionality."""
    print("🔍 Testing Navigator WebSocket...")
    
    try:
        # Import here to avoid import issues
        from api.routes.navigator_websocket import NavigatorConnectionManager
        
        # Create a mock connection manager
        manager = NavigatorConnectionManager()
        
        # Test connection tracking
        print("✅ Connection manager created successfully")
        print(f"   Active connections: {len(manager.active_connections)}")
        print(f"   User connections: {len(manager.user_connections)}")
        print(f"   Workspace connections: {len(manager.workspace_connections)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Navigator WebSocket test failed: {e}")
        return False

async def test_workspace_service():
    """Test the Workspace service functionality."""
    print("🔍 Testing Workspace Service...")
    
    try:
        from api.services.workspace_service import WorkspaceService
        from api.schemas.workspace import WorkspaceCreate, WorkspaceRole
        
        # Create a mock database session
        class MockDB:
            def query(self, model):
                return MockQuery()
            
            def add(self, obj):
                pass
            
            def commit(self):
                pass
            
            def flush(self):
                pass
            
            def execute(self, stmt):
                return MockResult()
        
        class MockQuery:
            def filter(self, *args):
                return self
            
            def join(self, *args):
                return self
            
            def outerjoin(self, *args):
                return self
            
            def order_by(self, *args):
                return self
            
            def offset(self, n):
                return self
            
            def limit(self, n):
                return self
            
            def all(self):
                return []
            
            def first(self):
                return None
            
            def count(self):
                return 0
        
        class MockResult:
            def first(self):
                return None
        
        # Test the service
        service = WorkspaceService(MockDB())
        
        print("✅ Workspace service created successfully")
        print("✅ Service methods available:")
        print("   - create_workspace")
        print("   - list_workspaces") 
        print("   - get_workspace")
        print("   - update_workspace")
        print("   - delete_workspace")
        print("   - add_member")
        print("   - log_activity")
        
        return True
        
    except Exception as e:
        print(f"❌ Workspace service test failed: {e}")
        return False

async def test_bas_service():
    """Test the BAS service functionality."""
    print("🔍 Testing BAS Service...")
    
    try:
        from api.services.bas_service import BASService
        from api.models.database.bas import ControlType, ValidationFrequency
        
        # Create a mock database session
        class MockDB:
            def query(self, model):
                return MockQuery()
            
            def add(self, obj):
                pass
            
            def commit(self):
                pass
            
            def refresh(self, obj):
                pass
        
        class MockQuery:
            def filter(self, *args):
                return self
            
            def order_by(self, *args):
                return self
            
            def offset(self, n):
                return self
            
            def limit(self, n):
                return self
            
            def all(self):
                return []
            
            def first(self):
                return None
            
            def count(self):
                return 0
        
        # Test the service
        service = BASService(MockDB())
        
        print("✅ BAS service created successfully")
        print("✅ Service methods available:")
        print("   - create_security_control")
        print("   - list_security_controls")
        print("   - create_validation_schedule")
        print("   - execute_validation")
        print("   - create_bas_campaign")
        print("   - execute_bas_campaign")
        print("   - get_bas_statistics")
        print("   - get_control_health_status")
        
        # Test enum values
        print("✅ Control types available:")
        for control_type in ControlType:
            print(f"   - {control_type.value}")
        
        print("✅ Validation frequencies available:")
        for frequency in ValidationFrequency:
            print(f"   - {frequency.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ BAS service test failed: {e}")
        return False

async def test_api_routes():
    """Test that API routes can be imported."""
    print("🔍 Testing API Routes...")
    
    try:
        # Test Navigator WebSocket routes
        from api.routes.navigator_websocket import router as navigator_ws_router
        print("✅ Navigator WebSocket router imported successfully")
        print(f"   Prefix: {navigator_ws_router.prefix}")
        print(f"   Tags: {navigator_ws_router.tags}")
        
        # Test Workspace routes
        from api.routes.workspace import router as workspace_router
        print("✅ Workspace router imported successfully")
        print(f"   Prefix: {workspace_router.prefix}")
        print(f"   Tags: {workspace_router.tags}")
        
        # Test BAS routes
        from api.routes.bas import router as bas_router
        print("✅ BAS router imported successfully")
        print(f"   Prefix: {bas_router.prefix}")
        print(f"   Tags: {bas_router.tags}")
        
        return True
        
    except Exception as e:
        print(f"❌ API routes test failed: {e}")
        return False

async def test_frontend_types():
    """Test that frontend types are properly structured."""
    print("🔍 Testing Frontend Types...")
    
    try:
        # Check if TypeScript files exist
        workspace_types_path = "frontend/src/types/workspace.ts"
        navigator_types_path = "frontend/src/types/navigator.ts"
        
        if os.path.exists(workspace_types_path):
            print("✅ Workspace TypeScript types file exists")
            with open(workspace_types_path, 'r') as f:
                content = f.read()
                if "WorkspaceRole" in content and "ActivityType" in content:
                    print("✅ Workspace types contain expected enums and interfaces")
                else:
                    print("⚠️  Workspace types may be incomplete")
        else:
            print("❌ Workspace types file not found")
        
        if os.path.exists(navigator_types_path):
            print("✅ Navigator TypeScript types file exists")
            with open(navigator_types_path, 'r') as f:
                content = f.read()
                if "NavigatorLayer" in content and "NavigatorTechnique" in content:
                    print("✅ Navigator types contain expected interfaces")
                else:
                    print("⚠️  Navigator types may be incomplete")
        else:
            print("❌ Navigator types file not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Frontend types test failed: {e}")
        return False

async def test_database_models():
    """Test that database models can be imported."""
    print("🔍 Testing Database Models...")
    
    try:
        # Test Workspace models
        from api.models.database.workspace import (
            Workspace, WorkspaceActivity, WorkspaceInvitation
        )
        print("✅ Workspace models imported successfully")
        print("   - Workspace")
        print("   - WorkspaceActivity") 
        print("   - WorkspaceInvitation")
        
        # Test BAS models
        from api.models.database.bas import (
            SecurityControl, ValidationSchedule, ValidationResult,
            BASCampaign, BASExecution
        )
        print("✅ BAS models imported successfully")
        print("   - SecurityControl")
        print("   - ValidationSchedule")
        print("   - ValidationResult")
        print("   - BASCampaign")
        print("   - BASExecution")
        
        return True
        
    except Exception as e:
        print(f"❌ Database models test failed: {e}")
        return False

async def main():
    """Run all Phase 1B tests."""
    print("🧪 Testing Phase 1B Implementation")
    print("=" * 50)
    
    tests = [
        ("Navigator WebSocket", test_navigator_websocket),
        ("Workspace Service", test_workspace_service),
        ("BAS Service", test_bas_service),
        ("API Routes", test_api_routes),
        ("Frontend Types", test_frontend_types),
        ("Database Models", test_database_models),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 1B tests passed!")
        print("\n🚀 Ready for Phase 2 development:")
        print("   - Integration ecosystem (SIEM, EDR, SOAR)")
        print("   - Multi-executor support")
        print("   - Advanced analytics")
        print("   - Threat intelligence integration")
        return 0
    else:
        print("⚠️  Some tests failed - review implementation")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
