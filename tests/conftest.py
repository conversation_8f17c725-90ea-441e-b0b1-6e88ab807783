"""Test configuration and fixtures."""
import os
import sys
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from pathlib import Path
import logging
from datetime import timedelta

# Configure logging with more verbose output for test debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

# Import all necessary models and components
from api.database import Base, engine, get_db, init_db
from api.main import app as db_app
from api.models.base import CampaignDB, TestCaseDB, OrganizationDB, CampaignToOrganizationDB
from api.models.user import User, UserRole
from api.auth.router import create_access_token

def clear_db():
    """Clear all database objects and recreate tables in correct order."""
    try:
        logger.info("Starting database cleanup")
        init_db(clean=True)  # Use the enhanced init_db with clean parameter
        logger.info("Successfully reset database")
    except SQLAlchemyError as e:
        logger.error(f"Database error during cleanup: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error during cleanup: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
        raise

@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine."""
    try:
        # Clear database first
        logger.info("Setting up test database engine")
        clear_db()
        yield engine
        # Cleanup after all tests
        clear_db()
    except Exception as e:
        logger.error(f"Error in db_engine fixture: {e}", exc_info=True)
        raise

@pytest.fixture(scope="function")
def db_session(db_engine) -> Session:
    """Create a fresh database session for each test."""
    logger.debug("Creating new test database session")
    connection = db_engine.connect()
    transaction = connection.begin()

    session_factory = sessionmaker(
        bind=connection,
        autocommit=False,
        autoflush=False,
        expire_on_commit=False
    )
    session = session_factory()

    try:
        yield session
    finally:
        logger.debug("Cleaning up test database session")
        session.close()
        transaction.rollback()
        connection.close()

@pytest.fixture(scope="session")
def test_app():
    """Create a test FastAPI application instance."""
    logger.debug("Creating test FastAPI application")
    return db_app

@pytest.fixture(scope="function")
def client(test_app, db_session) -> TestClient:
    """Create a test client with database session override."""
    logger.debug("Creating test client")

    def override_get_db():
        try:
            yield db_session
        finally:
            pass  # Session cleanup handled by db_session fixture

    test_app.dependency_overrides[get_db] = override_get_db
    with TestClient(test_app) as test_client:
        yield test_client

    test_app.dependency_overrides.clear()
    logger.debug("Test client cleanup complete")

@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    logger.debug("Creating test user")
    user = User(
        username="testuser",
        email="<EMAIL>",
        role=UserRole.ANALYST
    )
    user.set_password("testpass123")  # Uses the User model's password hashing method
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def admin_user(db_session):
    """Create an admin user."""
    logger.debug("Creating admin user")
    user = User(
        username="admin",
        email="<EMAIL>",
        role=UserRole.ADMIN
    )
    user.set_password("adminpass123")
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def admin_token(admin_user):
    """Get an admin token for testing."""
    logger.debug("Creating admin token")
    access_token = create_access_token(
        data={"sub": admin_user.username},
        expires_delta=timedelta(minutes=30)
    )
    return access_token

@pytest.fixture
def user_token(test_user):
    """Get a regular user token for testing."""
    logger.debug("Creating user token")
    access_token = create_access_token(
        data={"sub": test_user.username},
        expires_delta=timedelta(minutes=30)
    )
    return access_token