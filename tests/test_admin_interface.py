"""
Tests for the Admin Interface API.

This module contains tests for the admin interface endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from api.main import app
from models.admin_interface import (
    AdminSettingCreate, AdminSettingUpdate,
    SystemConfigurationCreate, SystemConfigurationUpdate,
    AdminDashboardWidgetCreate, AdminDashboardWidgetUpdate,
    AdminNotificationCreate, AdminNotificationUpdate
)
from api.models.user import User, UserRole
from api.models.database.admin_interface import (
    AdminSetting, SystemConfiguration, 
    AdminDashboardWidget, AdminNotification
)
from api.models.audit import AuditLog
from datetime import datetime, timedelta
import json
from unittest.mock import patch, MagicMock
from api.auth.utils import create_access_token

client = TestClient(app)

# Mock authentication to bypass the admin-only restriction
@pytest.fixture(autouse=True)
def mock_get_current_admin_user():
    """Mock the get_current_admin_user dependency to bypass authentication"""
    with patch("api.endpoints.admin_interface.get_current_admin_user") as mock:
        # Create a mock admin user
        admin_user = MagicMock(spec=User)
        admin_user.id = 1
        admin_user.username = "admin"
        admin_user.email = "<EMAIL>"
        admin_user.role = UserRole.ADMIN
        admin_user.is_active = True
        mock.return_value = admin_user
        yield mock

# Mock database session
@pytest.fixture
def mock_db():
    """Mock the database session"""
    with patch("api.endpoints.admin_interface.get_db") as mock:
        # Create a mock DB session
        db_session = MagicMock()
        
        # Mock query method to return a query builder
        query_builder = MagicMock()
        db_session.query.return_value = query_builder
        
        # Mock filter method to return itself
        query_builder.filter.return_value = query_builder
        
        # Mock offset and limit methods to return itself
        query_builder.offset.return_value = query_builder
        query_builder.limit.return_value = query_builder
        
        # Mock order_by method to return itself
        query_builder.order_by.return_value = query_builder
        
        # Mock all method to return an empty list by default
        query_builder.all.return_value = []
        
        # Mock first method to return None by default
        query_builder.first.return_value = None
        
        # Mock scalar method to return 0 by default
        query_builder.scalar.return_value = 0
        
        mock.return_value = db_session
        yield db_session

# Sample data fixtures
@pytest.fixture
def sample_admin_setting_data():
    """Fixture for sample admin setting data"""
    return {
        "key": "test_setting",
        "value": "test_value",
        "description": "Test setting description",
        "category": "test",
        "is_editable": True
    }

@pytest.fixture
def sample_system_config_data():
    """Fixture for sample system configuration data"""
    return {
        "name": "test_config",
        "value": "test_value",
        "description": "Test configuration description",
        "is_encrypted": False
    }

@pytest.fixture
def sample_dashboard_widget_data():
    """Fixture for sample dashboard widget data"""
    return {
        "name": "Test Widget",
        "widget_type": "chart",
        "position": 1,
        "size": "medium",
        "config": {"type": "bar", "data": {"labels": [], "datasets": []}},
        "is_enabled": True
    }

@pytest.fixture
def sample_notification_data():
    """Fixture for sample notification data"""
    return {
        "title": "Test Notification",
        "message": "This is a test notification",
        "severity": "info",
        "user_id": None  # For all admins
    }

# Admin Dashboard Tests
def test_get_admin_dashboard(mock_db):
    """Test getting the admin dashboard"""
    # Mock the query results
    mock_db.query().filter().all.return_value = []  # Empty widgets
    mock_db.query().filter().order_by().limit().all.return_value = []  # Empty notifications
    mock_db.query().order_by().limit().all.return_value = []  # Empty audit logs
    
    # Mock the count queries
    mock_db.query().scalar.return_value = 5  # Total users
    mock_db.query().filter().scalar.return_value = 3  # Active users
    
    response = client.get("/api/v1/admin/dashboard")
    assert response.status_code == 200
    data = response.json()
    
    # Check the structure of the response
    assert "stats" in data
    assert "widgets" in data
    assert "notifications" in data
    assert "recent_audit_logs" in data
    
    # Check the stats
    assert data["stats"]["total_users"] == 5
    assert data["stats"]["active_users"] == 3
    assert "system_uptime" in data["stats"]
    assert "database_size" in data["stats"]
    assert "error_count_24h" in data["stats"]
    assert "api_requests_24h" in data["stats"]

# Admin Settings Tests
def test_get_all_admin_settings(mock_db):
    """Test getting all admin settings"""
    # Mock the query results
    mock_setting = MagicMock(spec=AdminSetting)
    mock_setting.id = 1
    mock_setting.key = "test_setting"
    mock_setting.value = "test_value"
    mock_setting.description = "Test setting description"
    mock_setting.category = "test"
    mock_setting.is_editable = True
    mock_setting.created_at = datetime.now()
    mock_setting.updated_at = None
    
    mock_db.query().offset().limit().all.return_value = [mock_setting]
    
    response = client.get("/api/v1/admin/settings")
    assert response.status_code == 200
    data = response.json()
    
    assert isinstance(data, list)
    assert len(data) == 1
    assert data[0]["key"] == "test_setting"
    assert data[0]["value"] == "test_value"
    assert data[0]["category"] == "test"

def test_get_admin_setting(mock_db):
    """Test getting a specific admin setting"""
    # Mock the query result
    mock_setting = MagicMock(spec=AdminSetting)
    mock_setting.id = 1
    mock_setting.key = "test_setting"
    mock_setting.value = "test_value"
    mock_setting.description = "Test setting description"
    mock_setting.category = "test"
    mock_setting.is_editable = True
    mock_setting.created_at = datetime.now()
    mock_setting.updated_at = None
    
    mock_db.query().filter().first.return_value = mock_setting
    
    response = client.get("/api/v1/admin/settings/1")
    assert response.status_code == 200
    data = response.json()
    
    assert data["id"] == 1
    assert data["key"] == "test_setting"
    assert data["value"] == "test_value"
    assert data["category"] == "test"

def test_get_admin_setting_not_found(mock_db):
    """Test getting a non-existent admin setting"""
    # Mock the query result to return None
    mock_db.query().filter().first.return_value = None
    
    response = client.get("/api/v1/admin/settings/999")
    assert response.status_code == 404
    assert "detail" in response.json()
    assert response.json()["detail"] == "Admin setting not found"

def test_create_admin_setting(mock_db, sample_admin_setting_data):
    """Test creating an admin setting"""
    # Mock the query result for checking existing setting
    mock_db.query().filter().first.return_value = None
    
    # Mock the created setting
    mock_setting = MagicMock(spec=AdminSetting)
    mock_setting.id = 1
    mock_setting.key = sample_admin_setting_data["key"]
    mock_setting.value = sample_admin_setting_data["value"]
    mock_setting.description = sample_admin_setting_data["description"]
    mock_setting.category = sample_admin_setting_data["category"]
    mock_setting.is_editable = sample_admin_setting_data["is_editable"]
    mock_setting.created_at = datetime.now()
    mock_setting.updated_at = None
    
    # Mock the add, commit, and refresh methods
    def mock_add(obj):
        # Set attributes from the input object to our mock
        for key, value in sample_admin_setting_data.items():
            setattr(mock_setting, key, value)
    
    mock_db.add.side_effect = mock_add
    
    response = client.post("/api/v1/admin/settings", json=sample_admin_setting_data)
    assert response.status_code == 200
    data = response.json()
    
    assert data["key"] == sample_admin_setting_data["key"]
    assert data["value"] == sample_admin_setting_data["value"]
    assert data["category"] == sample_admin_setting_data["category"]
    assert "id" in data
    assert "created_at" in data

def test_create_admin_setting_duplicate(mock_db, sample_admin_setting_data):
    """Test creating an admin setting with a duplicate key"""
    # Mock the query result for checking existing setting
    mock_existing_setting = MagicMock(spec=AdminSetting)
    mock_existing_setting.id = 1
    mock_existing_setting.key = sample_admin_setting_data["key"]
    
    mock_db.query().filter().first.return_value = mock_existing_setting
    
    response = client.post("/api/v1/admin/settings", json=sample_admin_setting_data)
    assert response.status_code == 400
    assert "detail" in response.json()
    assert f"Setting with key '{sample_admin_setting_data['key']}' already exists" in response.json()["detail"]

def test_update_admin_setting(mock_db, sample_admin_setting_data):
    """Test updating an admin setting"""
    # Mock the query result
    mock_setting = MagicMock(spec=AdminSetting)
    mock_setting.id = 1
    mock_setting.key = sample_admin_setting_data["key"]
    mock_setting.value = sample_admin_setting_data["value"]
    mock_setting.description = sample_admin_setting_data["description"]
    mock_setting.category = sample_admin_setting_data["category"]
    mock_setting.is_editable = True
    mock_setting.created_at = datetime.now()
    mock_setting.updated_at = None
    
    mock_db.query().filter().first.return_value = mock_setting
    
    # Update data
    update_data = {
        "value": "updated_value",
        "description": "Updated description"
    }
    
    response = client.put("/api/v1/admin/settings/1", json=update_data)
    assert response.status_code == 200
    data = response.json()
    
    assert data["id"] == 1
    assert data["key"] == sample_admin_setting_data["key"]
    assert data["value"] == update_data["value"]
    assert data["description"] == update_data["description"]
    assert data["category"] == sample_admin_setting_data["category"]

def test_update_admin_setting_not_editable(mock_db, sample_admin_setting_data):
    """Test updating a non-editable admin setting"""
    # Mock the query result
    mock_setting = MagicMock(spec=AdminSetting)
    mock_setting.id = 1
    mock_setting.key = sample_admin_setting_data["key"]
    mock_setting.value = sample_admin_setting_data["value"]
    mock_setting.description = sample_admin_setting_data["description"]
    mock_setting.category = sample_admin_setting_data["category"]
    mock_setting.is_editable = False
    mock_setting.created_at = datetime.now()
    mock_setting.updated_at = None
    
    mock_db.query().filter().first.return_value = mock_setting
    
    # Update data
    update_data = {
        "value": "updated_value",
        "description": "Updated description"
    }
    
    response = client.put("/api/v1/admin/settings/1", json=update_data)
    assert response.status_code == 403
    assert "detail" in response.json()
    assert response.json()["detail"] == "This setting cannot be edited"

def test_delete_admin_setting(mock_db, sample_admin_setting_data):
    """Test deleting an admin setting"""
    # Mock the query result
    mock_setting = MagicMock(spec=AdminSetting)
    mock_setting.id = 1
    mock_setting.key = sample_admin_setting_data["key"]
    mock_setting.category = sample_admin_setting_data["category"]
    
    mock_db.query().filter().first.return_value = mock_setting
    
    response = client.delete("/api/v1/admin/settings/1")
    assert response.status_code == 200
    assert response.json()["message"] == "Admin setting deleted"
    
    # Verify delete was called
    mock_db.delete.assert_called_once()
    mock_db.commit.assert_called()

# Audit Log Tests
def test_get_audit_logs(mock_db):
    """Test getting audit logs"""
    # Mock the query results
    mock_audit_log = MagicMock(spec=AuditLog)
    mock_audit_log.id = 1
    mock_audit_log.user_id = 1
    mock_audit_log.action = "create"
    mock_audit_log.resource_type = "admin_setting"
    mock_audit_log.resource_id = "1"
    mock_audit_log.details = {"key": "test_setting"}
    mock_audit_log.ip_address = "127.0.0.1"
    mock_audit_log.user_agent = "test-agent"
    mock_audit_log.created_at = datetime.now()
    
    mock_db.query().order_by().offset().limit().all.return_value = [mock_audit_log]
    
    response = client.get("/api/v1/admin/audit-logs")
    assert response.status_code == 200
    data = response.json()
    
    assert isinstance(data, list)
    assert len(data) == 1
    assert data[0]["user_id"] == 1
    assert data[0]["action"] == "create"
    assert data[0]["resource_type"] == "admin_setting"
    assert data[0]["resource_id"] == "1"
    assert data[0]["details"] == {"key": "test_setting"}

def test_get_audit_log(mock_db):
    """Test getting a specific audit log"""
    # Mock the query result
    mock_audit_log = MagicMock(spec=AuditLog)
    mock_audit_log.id = 1
    mock_audit_log.user_id = 1
    mock_audit_log.action = "create"
    mock_audit_log.resource_type = "admin_setting"
    mock_audit_log.resource_id = "1"
    mock_audit_log.details = {"key": "test_setting"}
    mock_audit_log.ip_address = "127.0.0.1"
    mock_audit_log.user_agent = "test-agent"
    mock_audit_log.created_at = datetime.now()
    
    mock_db.query().filter().first.return_value = mock_audit_log
    
    response = client.get("/api/v1/admin/audit-logs/1")
    assert response.status_code == 200
    data = response.json()
    
    assert data["id"] == 1
    assert data["user_id"] == 1
    assert data["action"] == "create"
    assert data["resource_type"] == "admin_setting"
    assert data["resource_id"] == "1"
    assert data["details"] == {"key": "test_setting"}

# System Configuration Tests
def test_get_system_configurations(mock_db):
    """Test getting all system configurations"""
    # Mock the query results
    mock_config = MagicMock(spec=SystemConfiguration)
    mock_config.id = 1
    mock_config.name = "test_config"
    mock_config.value = "test_value"
    mock_config.description = "Test configuration description"
    mock_config.is_encrypted = False
    mock_config.created_at = datetime.now()
    mock_config.updated_at = None
    
    mock_db.query().offset().limit().all.return_value = [mock_config]
    
    response = client.get("/api/v1/admin/system-configs")
    assert response.status_code == 200
    data = response.json()
    
    assert isinstance(data, list)
    assert len(data) == 1
    assert data[0]["name"] == "test_config"
    assert data[0]["value"] == "test_value"
    assert data[0]["is_encrypted"] == False

def test_create_system_configuration(mock_db, sample_system_config_data):
    """Test creating a system configuration"""
    # Mock the query result for checking existing config
    mock_db.query().filter().first.return_value = None
    
    # Mock the created config
    mock_config = MagicMock(spec=SystemConfiguration)
    mock_config.id = 1
    mock_config.name = sample_system_config_data["name"]
    mock_config.value = sample_system_config_data["value"]
    mock_config.description = sample_system_config_data["description"]
    mock_config.is_encrypted = sample_system_config_data["is_encrypted"]
    mock_config.created_at = datetime.now()
    mock_config.updated_at = None
    
    # Mock the add, commit, and refresh methods
    def mock_add(obj):
        # Set attributes from the input object to our mock
        for key, value in sample_system_config_data.items():
            setattr(mock_config, key, value)
    
    mock_db.add.side_effect = mock_add
    
    response = client.post("/api/v1/admin/system-configs", json=sample_system_config_data)
    assert response.status_code == 200
    data = response.json()
    
    assert data["name"] == sample_system_config_data["name"]
    assert data["value"] == sample_system_config_data["value"]
    assert data["description"] == sample_system_config_data["description"]
    assert data["is_encrypted"] == sample_system_config_data["is_encrypted"]
    assert "id" in data
    assert "created_at" in data

# Dashboard Widget Tests
def test_get_dashboard_widgets(mock_db):
    """Test getting all dashboard widgets"""
    # Mock the query results
    mock_widget = MagicMock(spec=AdminDashboardWidget)
    mock_widget.id = 1
    mock_widget.name = "Test Widget"
    mock_widget.widget_type = "chart"
    mock_widget.position = 1
    mock_widget.size = "medium"
    mock_widget.config = {"type": "bar", "data": {"labels": [], "datasets": []}}
    mock_widget.is_enabled = True
    mock_widget.created_at = datetime.now()
    mock_widget.updated_at = None
    
    mock_db.query().order_by().offset().limit().all.return_value = [mock_widget]
    
    response = client.get("/api/v1/admin/widgets")
    assert response.status_code == 200
    data = response.json()
    
    assert isinstance(data, list)
    assert len(data) == 1
    assert data[0]["name"] == "Test Widget"
    assert data[0]["widget_type"] == "chart"
    assert data[0]["position"] == 1
    assert data[0]["size"] == "medium"
    assert data[0]["is_enabled"] == True

def test_create_dashboard_widget(mock_db, sample_dashboard_widget_data):
    """Test creating a dashboard widget"""
    # Mock the created widget
    mock_widget = MagicMock(spec=AdminDashboardWidget)
    mock_widget.id = 1
    mock_widget.name = sample_dashboard_widget_data["name"]
    mock_widget.widget_type = sample_dashboard_widget_data["widget_type"]
    mock_widget.position = sample_dashboard_widget_data["position"]
    mock_widget.size = sample_dashboard_widget_data["size"]
    mock_widget.config = sample_dashboard_widget_data["config"]
    mock_widget.is_enabled = sample_dashboard_widget_data["is_enabled"]
    mock_widget.created_at = datetime.now()
    mock_widget.updated_at = None
    
    # Mock the add, commit, and refresh methods
    def mock_add(obj):
        # Set attributes from the input object to our mock
        for key, value in sample_dashboard_widget_data.items():
            setattr(mock_widget, key, value)
    
    mock_db.add.side_effect = mock_add
    
    response = client.post("/api/v1/admin/widgets", json=sample_dashboard_widget_data)
    assert response.status_code == 200
    data = response.json()
    
    assert data["name"] == sample_dashboard_widget_data["name"]
    assert data["widget_type"] == sample_dashboard_widget_data["widget_type"]
    assert data["position"] == sample_dashboard_widget_data["position"]
    assert data["size"] == sample_dashboard_widget_data["size"]
    assert data["config"] == sample_dashboard_widget_data["config"]
    assert data["is_enabled"] == sample_dashboard_widget_data["is_enabled"]
    assert "id" in data
    assert "created_at" in data

# Admin Notification Tests
def test_get_admin_notifications(mock_db):
    """Test getting admin notifications"""
    # Mock the query results
    mock_notification = MagicMock(spec=AdminNotification)
    mock_notification.id = 1
    mock_notification.title = "Test Notification"
    mock_notification.message = "This is a test notification"
    mock_notification.severity = "info"
    mock_notification.is_read = False
    mock_notification.user_id = None
    mock_notification.created_at = datetime.now()
    
    mock_db.query().filter().order_by().offset().limit().all.return_value = [mock_notification]
    
    response = client.get("/api/v1/admin/notifications")
    assert response.status_code == 200
    data = response.json()
    
    assert isinstance(data, list)
    assert len(data) == 1
    assert data[0]["title"] == "Test Notification"
    assert data[0]["message"] == "This is a test notification"
    assert data[0]["severity"] == "info"
    assert data[0]["is_read"] == False
    assert data[0]["user_id"] is None

def test_create_admin_notification(mock_db, sample_notification_data):
    """Test creating an admin notification"""
    # Mock the created notification
    mock_notification = MagicMock(spec=AdminNotification)
    mock_notification.id = 1
    mock_notification.title = sample_notification_data["title"]
    mock_notification.message = sample_notification_data["message"]
    mock_notification.severity = sample_notification_data["severity"]
    mock_notification.is_read = False
    mock_notification.user_id = sample_notification_data["user_id"]
    mock_notification.created_at = datetime.now()
    
    # Mock the add, commit, and refresh methods
    def mock_add(obj):
        # Set attributes from the input object to our mock
        for key, value in sample_notification_data.items():
            setattr(mock_notification, key, value)
        setattr(mock_notification, "is_read", False)
    
    mock_db.add.side_effect = mock_add
    
    response = client.post("/api/v1/admin/notifications", json=sample_notification_data)
    assert response.status_code == 200
    data = response.json()
    
    assert data["title"] == sample_notification_data["title"]
    assert data["message"] == sample_notification_data["message"]
    assert data["severity"] == sample_notification_data["severity"]
    assert data["is_read"] == False
    assert data["user_id"] == sample_notification_data["user_id"]
    assert "id" in data
    assert "created_at" in data

def test_update_admin_notification(mock_db, sample_notification_data):
    """Test updating an admin notification (marking as read)"""
    # Mock the query result
    mock_notification = MagicMock(spec=AdminNotification)
    mock_notification.id = 1
    mock_notification.title = sample_notification_data["title"]
    mock_notification.message = sample_notification_data["message"]
    mock_notification.severity = sample_notification_data["severity"]
    mock_notification.is_read = False
    mock_notification.user_id = sample_notification_data["user_id"]
    mock_notification.created_at = datetime.now()
    
    mock_db.query().filter().first.return_value = mock_notification
    
    # Update data
    update_data = {
        "is_read": True
    }
    
    response = client.put("/api/v1/admin/notifications/1", json=update_data)
    assert response.status_code == 200
    data = response.json()
    
    assert data["id"] == 1
    assert data["title"] == sample_notification_data["title"]
    assert data["message"] == sample_notification_data["message"]
    assert data["severity"] == sample_notification_data["severity"]
    assert data["is_read"] == True
    assert data["user_id"] == sample_notification_data["user_id"]

# System Info Tests
def test_get_system_info():
    """Test getting system information"""
    response = client.get("/api/v1/admin/system-info")
    assert response.status_code == 200
    data = response.json()
    
    # Check the structure of the response
    assert "platform" in data
    assert "platform_version" in data
    assert "platform_release" in data
    assert "architecture" in data
    assert "processor" in data
    assert "hostname" in data
    assert "python_version" in data
    assert "cpu_count" in data
    assert "memory_total" in data
    assert "memory_available" in data
    assert "disk_total" in data
    assert "disk_free" in data
    assert "uptime_seconds" in data
