"""Tests for the authentication API endpoints."""
"""Authentication endpoint tests."""
import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
import json
from datetime import datetime, timedelta
import pyotp

from api.models.user import User, UserRole
from api.auth.utils import validate_password
from api.auth.router import create_access_token
from api.auth.dependencies import admin_only, operator_or_admin


@pytest.fixture
def test_user_data():
    """Test user data fixture."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "Password123!",
        "full_name": "Test User",
        "bio": "Test bio"
    }


@pytest.fixture
def test_user(db_session: Session, test_user_data):
    """Create a test user in the database."""
    user = User(
        username=test_user_data["username"],
        email=test_user_data["email"],
        role=UserRole.VIEWER,
        full_name=test_user_data["full_name"],
        bio=test_user_data["bio"],
        is_active=True,
        email_verified=True
    )
    user.set_password(test_user_data["password"])
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_admin_user(db_session: Session):
    """Create a test admin user in the database."""
    user = User(
        username="adminuser",
        email="<EMAIL>",
        role=UserRole.ADMIN,
        full_name="Admin User",
        is_active=True,
        email_verified=True
    )
    user.set_password("AdminPass123!")
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_user_token(test_user):
    """Generate a token for the test user."""
    return create_access_token(
        data={"sub": test_user.username, "role": test_user.role.value},
        expires_delta=timedelta(minutes=30)
    )


@pytest.fixture
def test_admin_token(test_admin_user):
    """Generate a token for the test admin user."""
    return create_access_token(
        data={"sub": test_admin_user.username, "role": test_admin_user.role.value},
        expires_delta=timedelta(minutes=30)
    )


@pytest.fixture
def auth_headers(test_user_token):
    """Authorization headers with test user token."""
    return {"Authorization": f"Bearer {test_user_token}"}


@pytest.fixture
def admin_auth_headers(test_admin_token):
    """Authorization headers with admin user token."""
    return {"Authorization": f"Bearer {test_admin_token}"}


def test_register_user(client: TestClient, db_session: Session, test_user_data):
    """Test user registration."""
    # Test valid registration
    response = client.post(
        "/auth/register/",
        json={
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "Password123!",
            "full_name": "New User",
            "bio": "New bio"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "newuser"
    assert data["email"] == "<EMAIL>"
    assert data["full_name"] == "New User"
    assert data["bio"] == "New bio"
    assert data["role"] == "viewer"
    assert data["is_active"] is True
    assert data["two_factor_enabled"] is False
    
    # Verify user was created in the database
    user = db_session.query(User).filter(User.username == "newuser").first()
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.role == UserRole.VIEWER
    assert user.is_active is True
    assert user.email_verified is False
    assert user.email_verification_token is not None
    assert user.email_verification_expires is not None
    
    # Test duplicate username
    response = client.post(
        "/auth/register/",
        json={
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "Password123!"
        }
    )
    assert response.status_code == 400
    assert "Username already exists" in response.json()["detail"]
    
    # Test duplicate email
    response = client.post(
        "/auth/register/",
        json={
            "username": "anotheruser",
            "email": "<EMAIL>",
            "password": "Password123!"
        }
    )
    assert response.status_code == 400
    assert "Email already exists" in response.json()["detail"]
    
    # Test weak password
    response = client.post(
        "/auth/register/",
        json={
            "username": "weakuser",
            "email": "<EMAIL>",
            "password": "weak"
        }
    )
    assert response.status_code == 400
    assert "Password must be at least 8 characters long" in response.json()["detail"]
    
    # Test invalid email format
    response = client.post(
        "/auth/register/",
        json={
            "username": "invalidemail",
            "email": "not-an-email",
            "password": "Password123!"
        }
    )
    assert response.status_code == 400
    assert "Invalid email format" in response.json()["detail"]


def test_verify_email(client: TestClient, db_session: Session):
    """Test email verification."""
    # Create a user with an email verification token
    user = User(
        username="verifyuser",
        email="<EMAIL>",
        role=UserRole.VIEWER,
        is_active=True,
        email_verified=False,
        email_verification_token="test-token",
        email_verification_expires=datetime.utcnow() + timedelta(hours=24)
    )
    user.set_password("Password123!")
    db_session.add(user)
    db_session.commit()
    
    # Test valid verification
    response = client.post(
        "/auth/verify-email/",
        json={"token": "test-token"}
    )
    assert response.status_code == 200
    assert "Email verified successfully" in response.json()["message"]
    
    # Verify user's email is now verified
    db_session.refresh(user)
    assert user.email_verified is True
    assert user.email_verification_token is None
    assert user.email_verification_expires is None
    
    # Test invalid token
    response = client.post(
        "/auth/verify-email/",
        json={"token": "invalid-token"}
    )
    assert response.status_code == 400
    assert "Invalid verification token" in response.json()["detail"]
    
    # Test expired token
    user2 = User(
        username="expireduser",
        email="<EMAIL>",
        role=UserRole.VIEWER,
        is_active=True,
        email_verified=False,
        email_verification_token="expired-token",
        email_verification_expires=datetime.utcnow() - timedelta(hours=1)
    )
    user2.set_password("Password123!")
    db_session.add(user2)
    db_session.commit()
    
    response = client.post(
        "/auth/verify-email/",
        json={"token": "expired-token"}
    )
    assert response.status_code == 400
    assert "Verification token has expired" in response.json()["detail"]


def test_resend_verification(client: TestClient, db_session: Session):
    """Test resending verification email."""
    # Create a user with unverified email
    user = User(
        username="unverifieduser",
        email="<EMAIL>",
        role=UserRole.VIEWER,
        is_active=True,
        email_verified=False
    )
    user.set_password("Password123!")
    db_session.add(user)
    db_session.commit()
    
    # Test resending verification
    response = client.post(
        "/auth/resend-verification/",
        json={"email": "<EMAIL>"}
    )
    assert response.status_code == 200
    assert "verification link has been sent" in response.json()["message"]
    
    # Verify token was generated
    db_session.refresh(user)
    assert user.email_verification_token is not None
    assert user.email_verification_expires is not None
    
    # Test already verified email
    user.email_verified = True
    db_session.commit()
    
    response = client.post(
        "/auth/resend-verification/",
        json={"email": "<EMAIL>"}
    )
    assert response.status_code == 200
    assert "Email is already verified" in response.json()["message"]
    
    # Test non-existent email
    response = client.post(
        "/auth/resend-verification/",
        json={"email": "<EMAIL>"}
    )
    assert response.status_code == 200
    assert "verification link has been sent" in response.json()["message"]


def test_login(client: TestClient, db_session: Session, test_user, test_user_data):
    """Test user login."""
    # Test valid login
    response = client.post(
        "/auth/token/",
        data={"username": test_user_data["username"], "password": test_user_data["password"]}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["access_token"] != ""
    assert data["token_type"] == "bearer"
    assert data["username"] == test_user_data["username"]
    assert data["role"] == "viewer"
    assert data["requires_two_factor"] is False
    
    # Test invalid password
    response = client.post(
        "/auth/token/",
        data={"username": test_user_data["username"], "password": "WrongPassword123!"}
    )
    assert response.status_code == 401
    assert "Incorrect username or password" in response.json()["detail"]
    
    # Test non-existent user
    response = client.post(
        "/auth/token/",
        data={"username": "nonexistentuser", "password": "Password123!"}
    )
    assert response.status_code == 401
    assert "Incorrect username or password" in response.json()["detail"]
    
    # Test inactive user
    test_user.is_active = False
    db_session.commit()
    
    response = client.post(
        "/auth/token/",
        data={"username": test_user_data["username"], "password": test_user_data["password"]}
    )
    assert response.status_code == 401
    
    # Reactivate user for other tests
    test_user.is_active = True
    db_session.commit()


def test_account_locking(client: TestClient, db_session: Session):
    """Test account locking after failed login attempts."""
    # Create a user
    user = User(
        username="lockuser",
        email="<EMAIL>",
        role=UserRole.VIEWER,
        is_active=True,
        email_verified=True,
        failed_login_attempts=0,
        account_locked=False
    )
    user.set_password("Password123!")
    db_session.add(user)
    db_session.commit()
    
    # Test failed login attempts
    for i in range(4):  # 4 failed attempts
        response = client.post(
            "/auth/token/",
            data={"username": "lockuser", "password": "WrongPassword123!"}
        )
        assert response.status_code == 401
        db_session.refresh(user)
        assert user.failed_login_attempts == i + 1
        assert user.account_locked is False
    
    # Fifth failed attempt should lock the account
    response = client.post(
        "/auth/token/",
        data={"username": "lockuser", "password": "WrongPassword123!"}
    )
    assert response.status_code == 401
    db_session.refresh(user)
    assert user.failed_login_attempts == 5
    assert user.account_locked is True
    assert user.account_locked_at is not None
    
    # Test login with locked account
    response = client.post(
        "/auth/token/",
        data={"username": "lockuser", "password": "Password123!"}
    )
    assert response.status_code == 401
    assert "Account locked" in response.json()["detail"]
    
    # Test account unlocking after time period
    user.account_locked_at = datetime.utcnow() - timedelta(hours=25)  # 25 hours ago (past the 24-hour lock)
    db_session.commit()
    
    response = client.post(
        "/auth/token/",
        data={"username": "lockuser", "password": "Password123!"}
    )
    assert response.status_code == 200
    db_session.refresh(user)
    assert user.account_locked is False
    assert user.failed_login_attempts == 0


def test_password_reset(client: TestClient, db_session: Session):
    """Test password reset functionality."""
    # Create a user
    user = User(
        username="resetuser",
        email="<EMAIL>",
        role=UserRole.VIEWER,
        is_active=True,
        email_verified=True
    )
    user.set_password("Password123!")
    db_session.add(user)
    db_session.commit()
    
    # Request password reset
    response = client.post(
        "/auth/request-password-reset/",
        json={"email": "<EMAIL>"}
    )
    assert response.status_code == 200
    assert "If your email is registered" in response.json()["message"]
    
    # Verify reset token was created
    db_session.refresh(user)
    assert user.password_reset_token is not None
    assert user.password_reset_expires is not None
    
    # Reset password
    reset_token = user.password_reset_token
    response = client.post(
        "/auth/reset-password/",
        json={"token": reset_token, "new_password": "NewPassword123!"}
    )
    assert response.status_code == 200
    assert "Password reset successful" in response.json()["message"]
    
    # Verify password was changed
    db_session.refresh(user)
    assert user.password_reset_token is None
    assert user.password_reset_expires is None
    assert user.password_changed_at is not None
    assert user.check_password("NewPassword123!")
    
    # Test login with new password
    response = client.post(
        "/auth/token/",
        data={"username": "resetuser", "password": "NewPassword123!"}
    )
    assert response.status_code == 200
    
    # Test invalid reset token
    response = client.post(
        "/auth/reset-password/",
        json={"token": "invalid-token", "new_password": "AnotherPassword123!"}
    )
    assert response.status_code == 400
    assert "Invalid reset token" in response.json()["detail"]
    
    # Test expired reset token
    user.password_reset_token = "expired-token"
    user.password_reset_expires = datetime.utcnow() - timedelta(hours=2)
    db_session.commit()
    
    response = client.post(
        "/auth/reset-password/",
        json={"token": "expired-token", "new_password": "AnotherPassword123!"}
    )
    assert response.status_code == 400
    assert "Reset token has expired" in response.json()["detail"]
    
    # Test weak password
    user.password_reset_token = "valid-token"
    user.password_reset_expires = datetime.utcnow() + timedelta(hours=1)
    db_session.commit()
    
    response = client.post(
        "/auth/reset-password/",
        json={"token": "valid-token", "new_password": "weak"}
    )
    assert response.status_code == 400
    assert "Password must be at least 8 characters long" in response.json()["detail"]


def test_change_password(client: TestClient, db_session: Session, test_user, auth_headers):
    """Test password change functionality."""
    # Test valid password change
    response = client.post(
        "/auth/change-password/",
        json={"current_password": "Password123!", "new_password": "NewPassword123!"},
        headers=auth_headers
    )
    assert response.status_code == 200
    assert "Password changed successfully" in response.json()["message"]
    
    # Verify password was changed
    db_session.refresh(test_user)
    assert test_user.password_changed_at is not None
    assert test_user.check_password("NewPassword123!")
    
    # Test login with new password
    response = client.post(
        "/auth/token/",
        data={"username": "testuser", "password": "NewPassword123!"}
    )
    assert response.status_code == 200
    
    # Test login with old password
    response = client.post(
        "/auth/token/",
        data={"username": "testuser", "password": "Password123!"}
    )
    assert response.status_code == 401
    
    # Test incorrect current password
    response = client.post(
        "/auth/change-password/",
        json={"current_password": "WrongPassword123!", "new_password": "AnotherPassword123!"},
        headers=auth_headers
    )
    assert response.status_code == 400
    assert "Incorrect current password" in response.json()["detail"]
    
    # Test weak new password
    response = client.post(
        "/auth/change-password/",
        json={"current_password": "NewPassword123!", "new_password": "weak"},
        headers=auth_headers
    )
    assert response.status_code == 400
    assert "Password must be at least 8 characters long" in response.json()["detail"]


def test_two_factor_setup(client: TestClient, db_session: Session, test_user, auth_headers):
    """Test two-factor authentication setup."""
    # Test 2FA setup
    response = client.post(
        "/auth/setup-2fa/",
        headers=auth_headers
    )
    assert response.status_code == 200
    data = response.json()
    assert "secret" in data
    assert "uri" in data
    assert "backup_codes" in data
    assert len(data["backup_codes"]) == 10
    
    # Verify secret was saved
    db_session.refresh(test_user)
    assert test_user.two_factor_secret is not None
    assert test_user.two_factor_enabled is False
    
    # Generate a valid TOTP token
    totp = pyotp.TOTP(test_user.two_factor_secret)
    valid_token = totp.now()
    
    # Test enabling 2FA
    response = client.post(
        "/auth/enable-2fa/",
        json={"token": valid_token},
        headers=auth_headers
    )
    assert response.status_code == 200
    assert "Two-factor authentication enabled successfully" in response.json()["message"]
    
    # Verify 2FA is enabled
    db_session.refresh(test_user)
    assert test_user.two_factor_enabled is True
    
    # Test setup when already enabled
    response = client.post(
        "/auth/setup-2fa/",
        headers=auth_headers
    )
    assert response.status_code == 400
    assert "Two-factor authentication is already enabled" in response.json()["detail"]


def test_two_factor_login(client: TestClient, db_session: Session):
    """Test login with two-factor authentication."""
    # Create a user with 2FA enabled
    user = User(
        username="2fauser",
        email="<EMAIL>",
        role=UserRole.VIEWER,
        is_active=True,
        email_verified=True,
        two_factor_enabled=True
    )
    user.set_password("Password123!")
    
    # Generate and set 2FA secret
    secret = pyotp.random_base32()
    user.two_factor_secret = secret
    db_session.add(user)
    db_session.commit()
    
    # Test login with 2FA required
    response = client.post(
        "/auth/token/",
        data={"username": "2fauser", "password": "Password123!"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["requires_two_factor"] is True
    assert data["two_factor_session_id"] is not None
    assert data["access_token"] == ""
    
    # Generate a valid TOTP token
    totp = pyotp.TOTP(secret)
    valid_token = totp.now()
    
    # Test 2FA verification
    response = client.post(
        "/auth/verify-2fa/",
        json={"token": valid_token, "is_backup_code": False},
        params={"session_id": data["two_factor_session_id"]}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["access_token"] != ""
    assert data["token_type"] == "bearer"
    assert data["username"] == "2fauser"
    assert data["role"] == "viewer"
    assert data["requires_two_factor"] is False
    
    # Test invalid 2FA token
    response = client.post(
        "/auth/token/",
        data={"username": "2fauser", "password": "Password123!"}
    )
    session_id = response.json()["two_factor_session_id"]
    
    response = client.post(
        "/auth/verify-2fa/",
        json={"token": "123456", "is_backup_code": False},
        params={"session_id": session_id}
    )
    assert response.status_code == 401
    assert "Invalid verification code" in response.json()["detail"]
    
    # Test invalid session
    response = client.post(
        "/auth/verify-2fa/",
        json={"token": valid_token, "is_backup_code": False},
        params={"session_id": "invalid-session"}
    )
    assert response.status_code == 401
    assert "Invalid or expired session" in response.json()["detail"]


def test_disable_two_factor(client: TestClient, db_session: Session, test_user, auth_headers):
    """Test disabling two-factor authentication."""
    # Enable 2FA for the test user
    test_user.two_factor_enabled = True
    test_user.two_factor_secret = pyotp.random_base32()
    db_session.commit()
    
    # Test disabling 2FA
    response = client.post(
        "/auth/disable-2fa/",
        json={"password": "Password123!"},
        headers=auth_headers
    )
    assert response.status_code == 200
    assert "Two-factor authentication disabled successfully" in response.json()["message"]
    
    # Verify 2FA is disabled
    db_session.refresh(test_user)
    assert test_user.two_factor_enabled is False
    assert test_user.two_factor_secret is None
    
    # Test disabling when not enabled
    response = client.post(
        "/auth/disable-2fa/",
        json={"password": "Password123!"},
        headers=auth_headers
    )
    assert response.status_code == 400
    assert "Two-factor authentication is not enabled" in response.json()["detail"]
    
    # Test incorrect password
    test_user.two_factor_enabled = True
    test_user.two_factor_secret = pyotp.random_base32()
    db_session.commit()
    
    response = client.post(
        "/auth/disable-2fa/",
        json={"password": "WrongPassword123!"},
        headers=auth_headers
    )
    assert response.status_code == 401
    assert "Incorrect password" in response.json()["detail"]


def test_regenerate_backup_codes(client: TestClient, db_session: Session, test_user, auth_headers):
    """Test regenerating backup codes."""
    # Enable 2FA for the test user
    test_user.two_factor_enabled = True
    test_user.two_factor_secret = pyotp.random_base32()
    test_user.backup_codes = json.dumps(["code1", "code2"])
    db_session.commit()
    
    # Test regenerating backup codes
    response = client.post(
        "/auth/regenerate-backup-codes/",
        json={"password": "Password123!"},
        headers=auth_headers
    )
    assert response.status_code == 200
    data = response.json()
    assert "backup_codes" in data
    assert len(data["backup_codes"]) == 10
    
    # Verify backup codes were changed
    db_session.refresh(test_user)
    assert test_user.backup_codes != json.dumps(["code1", "code2"])
    
    # Test when 2FA is not enabled
    test_user.two_factor_enabled = False
    db_session.commit()
    
    response = client.post(
        "/auth/regenerate-backup-codes/",
        json={"password": "Password123!"},
        headers=auth_headers
    )
    assert response.status_code == 400
    assert "Two-factor authentication is not enabled" in response.json()["detail"]
    
    # Test incorrect password
    test_user.two_factor_enabled = True
    db_session.commit()
    
    response = client.post(
        "/auth/regenerate-backup-codes/",
        json={"password": "WrongPassword123!"},
        headers=auth_headers
    )
    assert response.status_code == 401
    assert "Incorrect password" in response.json()["detail"]


def test_role_based_access(client: TestClient, db_session: Session, test_user, test_admin_user, auth_headers, admin_auth_headers):
    """Test role-based access control."""
    # Create a test endpoint that requires admin role
    from fastapi import FastAPI, Depends
    
    app = client.app
    
    @app.get("/test-admin-only")
    def admin_only_endpoint(user = Depends(admin_only)):
        return {"message": "Admin access granted"}
    
    @app.get("/test-operator-or-admin")
    def operator_or_admin_endpoint(user = Depends(operator_or_admin)):
        return {"message": "Operator or admin access granted"}
    
    # Test admin-only endpoint with admin user
    response = client.get("/test-admin-only", headers=admin_auth_headers)
    assert response.status_code == 200
    assert "Admin access granted" in response.json()["message"]
    
    # Test admin-only endpoint with regular user
    response = client.get("/test-admin-only", headers=auth_headers)
    assert response.status_code == 403
    assert "Insufficient permissions" in response.json()["detail"]
    
    # Test operator-or-admin endpoint with admin user
    response = client.get("/test-operator-or-admin", headers=admin_auth_headers)
    assert response.status_code == 200
    assert "Operator or admin access granted" in response.json()["message"]
    
    # Test operator-or-admin endpoint with regular user
    response = client.get("/test-operator-or-admin", headers=auth_headers)
    assert response.status_code == 403
    assert "Insufficient permissions" in response.json()["detail"]
    
    # Update regular user to operator role
    test_user.role = UserRole.OPERATOR
    db_session.commit()
    
    # Test operator-or-admin endpoint with operator user
    response = client.get("/test-operator-or-admin", headers=auth_headers)
    assert response.status_code == 200
    assert "Operator or admin access granted" in response.json()["message"]