"""Tests for authentication enhancements."""

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone

from api.main import app
from api.models.user import User, UserRole
from api.database import get_db
from tests.conftest import test_db


client = TestClient(app)


class TestAuthenticationEnhancements:
    """Test suite for authentication enhancements."""

    def test_register_user_success(self, test_db: Session):
        """Test successful user registration."""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "full_name": "Test User",
            "bio": "Test bio"
        }
        
        response = client.post("/api/register/", json=user_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["username"] == "testuser"
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "Test User"
        assert data["role"] == "viewer"
        assert not data["two_factor_enabled"]

    def test_register_user_duplicate_username(self, test_db: Session):
        """Test registration with duplicate username."""
        # Create first user
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "full_name": "Test User 1"
        }
        client.post("/api/register/", json=user_data)
        
        # Try to create second user with same username
        user_data2 = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "full_name": "Test User 2"
        }
        response = client.post("/api/register/", json=user_data2)
        assert response.status_code == 400
        assert "Username already exists" in response.json()["detail"]

    def test_register_user_weak_password(self, test_db: Session):
        """Test registration with weak password."""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "weak",
            "full_name": "Test User"
        }
        
        response = client.post("/api/register/", json=user_data)
        assert response.status_code == 400
        assert "Password must be at least" in response.json()["detail"]

    def test_login_success(self, test_db: Session):
        """Test successful login."""
        # Create user first
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.VIEWER,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Login
        login_data = {
            "username": "testuser",
            "password": "SecurePass123!"
        }
        response = client.post("/api/token/", data=login_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert data["username"] == "testuser"

    def test_login_invalid_credentials(self, test_db: Session):
        """Test login with invalid credentials."""
        login_data = {
            "username": "nonexistent",
            "password": "wrongpassword"
        }
        response = client.post("/api/token/", data=login_data)
        assert response.status_code == 401
        assert "Incorrect username or password" in response.json()["detail"]

    def test_account_lockout(self, test_db: Session):
        """Test account lockout after failed attempts."""
        # Create user
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.VIEWER,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Make 5 failed login attempts
        login_data = {
            "username": "testuser",
            "password": "wrongpassword"
        }
        for _ in range(5):
            response = client.post("/api/token/", data=login_data)
            assert response.status_code == 401
        
        # Check that account is locked
        test_db.refresh(user)
        assert user.account_locked

    def test_password_reset_request(self, test_db: Session):
        """Test password reset request."""
        # Create user
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.VIEWER,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Request password reset
        response = client.post("/api/request-password-reset/", json={"email": "<EMAIL>"})
        assert response.status_code == 200
        assert "password reset link has been sent" in response.json()["message"]

    def test_account_unlock_request(self, test_db: Session):
        """Test account unlock request."""
        # Create locked user
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.VIEWER,
            full_name="Test User",
            is_active=True,
            email_verified=True,
            account_locked=True,
            account_locked_at=datetime.now(timezone.utc)
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Request account unlock
        response = client.post("/api/unlock-account/", json={"email": "<EMAIL>"})
        assert response.status_code == 200
        assert "unlock link has been sent" in response.json()["message"]

    def test_get_current_user_info(self, test_db: Session):
        """Test getting current user information."""
        # Create user and get token
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.VIEWER,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "SecurePass123!"
        }
        login_response = client.post("/api/token/", data=login_data)
        token = login_response.json()["access_token"]
        
        # Get user info
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/me/", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["username"] == "testuser"
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "Test User"

    def test_update_current_user(self, test_db: Session):
        """Test updating current user information."""
        # Create user and get token
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.VIEWER,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "SecurePass123!"
        }
        login_response = client.post("/api/token/", data=login_data)
        token = login_response.json()["access_token"]
        
        # Update user info
        headers = {"Authorization": f"Bearer {token}"}
        update_data = {
            "full_name": "Updated Name",
            "bio": "Updated bio"
        }
        response = client.put("/api/me/", json=update_data, headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["full_name"] == "Updated Name"
        assert data["bio"] == "Updated bio"

    def test_change_password(self, test_db: Session):
        """Test changing password."""
        # Create user and get token
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.VIEWER,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "SecurePass123!"
        }
        login_response = client.post("/api/token/", data=login_data)
        token = login_response.json()["access_token"]
        
        # Change password
        headers = {"Authorization": f"Bearer {token}"}
        password_data = {
            "current_password": "SecurePass123!",
            "new_password": "NewSecurePass456!"
        }
        response = client.post("/api/change-password/", json=password_data, headers=headers)
        assert response.status_code == 200
        assert "Password changed successfully" in response.json()["message"]
