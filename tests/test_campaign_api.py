"""
Tests for the Campaign Management API.

This module contains tests for the campaign endpoints.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from api.main import app
from api.models.campaign import CampaignDB as Campaign, CampaignTestCase
from api.models.user import User
from api.schemas.campaign import CampaignC<PERSON>, CampaignTestCaseCreate
from api.services.campaign import create_campaign
from api.auth.utils import create_access_token

client = TestClient(app)


@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    from api.database import get_db
    db = next(get_db())
    yield db


@pytest.fixture
def admin_token():
    """Create an admin token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "admin"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def user_token():
    """Create a regular user token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "user"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def test_user(db: Session) -> User:
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_superuser=False
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture
def test_campaign(db: Session, test_user: User) -> Campaign:
    campaign = Campaign(
        name="Test Campaign",
        description="Test Description",
        status="draft",
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(days=30),
        created_by=test_user.id
    )
    db.add(campaign)
    db.commit()
    db.refresh(campaign)
    return campaign


def test_get_campaigns(admin_token):
    """Test getting a list of campaigns."""
    response = client.get(
        "/api/v1/campaigns/",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_get_campaign(admin_token, test_campaign):
    """Test getting a specific campaign."""
    response = client.get(
        f"/api/v1/campaigns/{test_campaign.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_campaign.id
    assert response.json()["name"] == test_campaign.name


def test_create_campaign(db: Session, test_user: User):
    campaign_data = {
        "name": "New Campaign",
        "description": "New Description",
        "status": "draft",
        "start_date": datetime.utcnow().isoformat(),
        "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
    }
    
    response = client.post(
        "/campaigns/",
        json=campaign_data,
        headers={"Authorization": f"Bearer {test_user.id}"}
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == campaign_data["name"]
    assert data["description"] == campaign_data["description"]
    assert data["status"] == campaign_data["status"]
    assert data["created_by"] == test_user.id


def test_list_campaigns(db: Session, test_user: User, test_campaign: Campaign):
    response = client.get(
        "/campaigns/",
        headers={"Authorization": f"Bearer {test_user.id}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["items"]) == 1
    assert data["items"][0]["id"] == test_campaign.id


def test_update_campaign(db: Session, test_user: User, test_campaign: Campaign):
    update_data = {
        "name": "Updated Campaign",
        "status": "active"
    }
    
    response = client.put(
        f"/campaigns/{test_campaign.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {test_user.id}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["status"] == update_data["status"]


def test_delete_campaign(db: Session, test_user: User, test_campaign: Campaign):
    response = client.delete(
        f"/campaigns/{test_campaign.id}",
        headers={"Authorization": f"Bearer {test_user.id}"}
    )
    
    assert response.status_code == 204
    
    # Verify campaign is soft deleted
    db_campaign = db.query(Campaign).filter(Campaign.id == test_campaign.id).first()
    assert db_campaign.is_deleted == True


def test_restore_campaign(admin_token, test_campaign, db_session):
    """Test restoring a soft-deleted campaign."""
    # First, delete the campaign
    test_campaign.soft_delete(db_session)
    
    response = client.post(
        f"/api/v1/campaigns/{test_campaign.id}/restore",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_campaign.id
    assert response.json()["deleted_at"] is None


def test_assign_test_cases(admin_token, test_campaign):
    """Test assigning test cases to a campaign."""
    assignment_data = {
        "test_case_ids": [1, 2, 3]
    }
    
    response = client.post(
        f"/api/v1/campaigns/{test_campaign.id}/test-cases",
        json=assignment_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 204


def test_remove_test_case(admin_token, test_campaign):
    """Test removing a test case from a campaign."""
    response = client.delete(
        f"/api/v1/campaigns/{test_campaign.id}/test-cases/1",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 204


def test_get_campaign_summary(admin_token, test_campaign):
    """Test getting a campaign summary."""
    response = client.get(
        f"/api/v1/campaigns/{test_campaign.id}/summary",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert "total_test_cases" in response.json()
    assert "total_assessments" in response.json()
    assert "test_case_status" in response.json()
    assert "assessment_status" in response.json()
    assert "completion_percentage" in response.json()


def test_unauthorized_access(user_token):
    """Test unauthorized access to admin-only endpoints."""
    # Regular users can't restore campaigns
    response = client.post(
        "/api/v1/campaigns/1/restore",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403


def test_add_test_case(db: Session, test_user: User, test_campaign: Campaign):
    test_case_data = {
        "test_case_id": 1,
        "status": "pending",
        "assigned_to": test_user.id,
        "notes": "Test notes"
    }
    
    response = client.post(
        f"/campaigns/{test_campaign.id}/test-cases",
        json=test_case_data,
        headers={"Authorization": f"Bearer {test_user.id}"}
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["campaign_id"] == test_campaign.id
    assert data["test_case_id"] == test_case_data["test_case_id"]
    assert data["status"] == test_case_data["status"]


def test_update_test_case(db: Session, test_user: User, test_campaign: Campaign):
    # First create a test case
    test_case = CampaignTestCase(
        campaign_id=test_campaign.id,
        test_case_id=1,
        status="pending",
        assigned_to=test_user.id
    )
    db.add(test_case)
    db.commit()
    db.refresh(test_case)
    
    update_data = {
        "status": "in_progress",
        "notes": "Updated notes"
    }
    
    response = client.put(
        f"/campaigns/{test_campaign.id}/test-cases/{test_case.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {test_user.id}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == update_data["status"]
    assert data["notes"] == update_data["notes"]


def test_remove_test_case(db: Session, test_user: User, test_campaign: Campaign):
    # First create a test case
    test_case = CampaignTestCase(
        campaign_id=test_campaign.id,
        test_case_id=1,
        status="pending",
        assigned_to=test_user.id
    )
    db.add(test_case)
    db.commit()
    db.refresh(test_case)
    
    response = client.delete(
        f"/campaigns/{test_campaign.id}/test-cases/{test_case.id}",
        headers={"Authorization": f"Bearer {test_user.id}"}
    )
    
    assert response.status_code == 204
    
    # Verify test case is removed
    db_test_case = db.query(CampaignTestCase).filter(
        CampaignTestCase.id == test_case.id
    ).first()
    assert db_test_case is None 