"""
Tests for the Test Case Management API.

This module contains tests for the Test Case Management API endpoints.
"""
import json
import pytest
from datetime import timedelta
from fastapi.testclient import TestClient

from api.main import app
from api.models.database.test_case import TestCaseDB as TestCase
from api.models.schemas.test_case import TestCaseStatus, TestCaseType, TestCasePriority, TestCaseComplexity
from api.auth.utils import create_access_token


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Create an admin token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "admin"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def user_token():
    """Create a regular user token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "user"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def test_test_case(admin_token):
    """Create a test case for testing."""
    test_case_data = {
        "name": "Test SQL Injection",
        "description": "Test for SQL injection vulnerabilities",
        "type": TestCaseType.MANUAL.value,
        "status": TestCaseStatus.DRAFT.value,
        "priority": TestCasePriority.HIGH.value,
        "complexity": TestCaseComplexity.MODERATE.value,
        "prerequisites": "Access to the application",
        "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
        "expected_result": "Application should sanitize input and prevent SQL injection",
        "tags": ["security", "injection", "sql"],
        "mitre_techniques": ["T1190", "T1212"]
    }
    
    response = client.post(
        "/api/v1/test-cases",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=test_case_data
    )
    
    return response.json()


def test_create_test_case(admin_token):
    """Test creating a test case."""
    test_case_data = {
        "name": "Test XSS Vulnerability",
        "description": "Test for cross-site scripting vulnerabilities",
        "type": TestCaseType.MANUAL.value,
        "status": TestCaseStatus.DRAFT.value,
        "priority": TestCasePriority.HIGH.value,
        "complexity": TestCaseComplexity.MODERATE.value,
        "prerequisites": "Access to the application",
        "steps": ["Step 1: Navigate to input form", "Step 2: Enter XSS payload"],
        "expected_result": "Application should sanitize input and prevent XSS",
        "tags": ["security", "injection", "xss"],
        "mitre_techniques": ["T1059", "T1189"]
    }
    
    response = client.post(
        "/api/v1/test-cases",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=test_case_data
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == test_case_data["name"]
    assert data["description"] == test_case_data["description"]
    assert data["type"] == test_case_data["type"]
    assert data["status"] == test_case_data["status"]
    assert data["priority"] == test_case_data["priority"]
    assert data["complexity"] == test_case_data["complexity"]
    assert data["prerequisites"] == test_case_data["prerequisites"]
    assert data["steps"] == test_case_data["steps"]
    assert data["expected_result"] == test_case_data["expected_result"]
    assert data["tags"] == test_case_data["tags"]
    assert data["mitre_techniques"] == test_case_data["mitre_techniques"]
    assert "id" in data
    assert "created_at" in data
    assert "updated_at" in data


def test_get_test_cases(admin_token, test_test_case):
    """Test getting a list of test cases."""
    response = client.get(
        "/api/v1/test-cases",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 1
    
    # Test filtering
    response = client.get(
        f"/api/v1/test-cases?status={TestCaseStatus.DRAFT.value}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert all(tc["status"] == TestCaseStatus.DRAFT.value for tc in data)
    
    # Test search
    response = client.get(
        "/api/v1/test-cases?search=SQL",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert any("SQL" in tc["name"] or "SQL" in tc.get("description", "") for tc in data)


def test_get_test_case_by_id(admin_token, test_test_case):
    """Test getting a test case by ID."""
    response = client.get(
        f"/api/v1/test-cases/{test_test_case['id']}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_test_case["id"]
    assert data["name"] == test_test_case["name"]


def test_update_test_case(admin_token, test_test_case):
    """Test updating a test case."""
    update_data = {
        "name": "Updated SQL Injection Test",
        "status": TestCaseStatus.ACTIVE.value
    }
    
    response = client.put(
        f"/api/v1/test-cases/{test_test_case['id']}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=update_data
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_test_case["id"]
    assert data["name"] == update_data["name"]
    assert data["status"] == update_data["status"]
    assert data["description"] == test_test_case["description"]  # Unchanged field


def test_delete_test_case(admin_token, test_test_case):
    """Test deleting a test case."""
    response = client.delete(
        f"/api/v1/test-cases/{test_test_case['id']}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 204
    
    # Verify it's deleted
    response = client.get(
        f"/api/v1/test-cases/{test_test_case['id']}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 404


def test_restore_test_case(admin_token, test_test_case):
    """Test restoring a deleted test case."""
    # First delete the test case
    client.delete(
        f"/api/v1/test-cases/{test_test_case['id']}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    # Then restore it
    response = client.post(
        f"/api/v1/test-cases/{test_test_case['id']}/restore",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_test_case["id"]
    
    # Verify it's accessible again
    response = client.get(
        f"/api/v1/test-cases/{test_test_case['id']}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200


def test_deprecate_test_case(admin_token, test_test_case):
    """Test deprecating a test case."""
    response = client.post(
        f"/api/v1/test-cases/{test_test_case['id']}/deprecate",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={"reason": "Replaced by a newer test case"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_test_case["id"]
    assert data["status"] == TestCaseStatus.DEPRECATED.value
    assert data["is_deprecated"] is True


def test_revoke_test_case(admin_token, test_test_case):
    """Test revoking a test case."""
    response = client.post(
        f"/api/v1/test-cases/{test_test_case['id']}/revoke",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={"reason": "Test case contains incorrect information"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_test_case["id"]
    assert data["is_revoked"] is True


def test_bulk_create_test_cases(admin_token):
    """Test bulk creating test cases."""
    test_cases_data = {
        "test_cases": [
            {
                "name": "Bulk Test Case 1",
                "description": "First bulk test case",
                "expected_result": "Expected result 1"
            },
            {
                "name": "Bulk Test Case 2",
                "description": "Second bulk test case",
                "expected_result": "Expected result 2"
            },
            {
                "name": "Bulk Test Case 3",
                "description": "Third bulk test case",
                "expected_result": "Expected result 3"
            }
        ]
    }
    
    response = client.post(
        "/api/v1/test-cases/bulk",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=test_cases_data
    )
    
    assert response.status_code == 201
    data = response.json()
    assert "test_cases" in data
    assert len(data["test_cases"]) == 3
    assert data["total_created"] == 3
    assert len(data["failed_entries"]) == 0


def test_get_test_case_stats(admin_token, test_test_case):
    """Test getting test case statistics."""
    response = client.get(
        "/api/v1/test-cases/stats",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "total" in data
    assert "by_status" in data
    assert "by_type" in data
    assert "by_priority" in data
    assert "by_complexity" in data
    assert data["total"] >= 1


def test_unauthorized_access(user_token, test_test_case):
    """Test unauthorized access to admin-only endpoints."""
    # Regular user should not be able to restore a test case
    response = client.post(
        f"/api/v1/test-cases/{test_test_case['id']}/restore",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    
    assert response.status_code == 403
    
    # Regular user should not be able to revoke a test case
    response = client.post(
        f"/api/v1/test-cases/{test_test_case['id']}/revoke",
        headers={"Authorization": f"Bearer {user_token}"},
        json={"reason": "Test case contains incorrect information"}
    )
    
    assert response.status_code == 403 