"""Tests for testcase chaining UI enhancements."""

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime, timezone

from api.main import app
from api.models.user import User, UserRole
from api.models.database.testcase_chaining import (
    TestcaseChainDB, 
    TestcaseChainNodeDB, 
    TestcaseChainEdgeDB,
    ChainExecutionDB,
    NodeExecutionDB
)
from api.models.base import TestCaseDB, CampaignDB
from api.database import get_db
from tests.conftest import test_db


client = TestClient(app)


class TestTestcaseChainUI:
    """Test suite for testcase chaining UI functionality."""

    def test_create_testcase_chain_api(self, test_db: Session):
        """Test creating a testcase chain via API."""
        # Create user and get token
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.ANALYST,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "SecurePass123!"
        }
        login_response = client.post("/api/token/", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Create testcase chain
        chain_data = {
            "name": "Test Attack Chain",
            "description": "A test attack chain for UI testing",
            "status": "draft"
        }
        response = client.post("/api/v1/testcase-chains/", json=chain_data, headers=headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["name"] == "Test Attack Chain"
        assert data["description"] == "A test attack chain for UI testing"
        assert data["status"] == "draft"

    def test_get_testcase_chains_api(self, test_db: Session):
        """Test retrieving testcase chains via API."""
        # Create user and get token
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.ANALYST,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Create a test chain
        chain = TestcaseChainDB(
            name="Test Chain",
            description="Test Description",
            created_by=user.id,
            status="draft"
        )
        test_db.add(chain)
        test_db.commit()
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "SecurePass123!"
        }
        login_response = client.post("/api/token/", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Get chains
        response = client.get("/api/v1/testcase-chains/", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) >= 1
        assert any(c["name"] == "Test Chain" for c in data)

    def test_create_chain_node_api(self, test_db: Session):
        """Test creating a chain node via API."""
        # Create user and get token
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.ANALYST,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Create campaign and test case
        campaign = CampaignDB(
            name="Test Campaign",
            description="Test Description",
            created_by=user.id
        )
        test_db.add(campaign)
        test_db.commit()
        
        test_case = TestCaseDB(
            name="Test Case",
            description="Test Description",
            campaign_id=campaign.id,
            created_by=user.id
        )
        test_db.add(test_case)
        test_db.commit()
        
        # Create chain
        chain = TestcaseChainDB(
            name="Test Chain",
            description="Test Description",
            created_by=user.id,
            status="draft"
        )
        test_db.add(chain)
        test_db.commit()
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "SecurePass123!"
        }
        login_response = client.post("/api/token/", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Create node
        node_data = {
            "chain_id": chain.id,
            "testcase_id": test_case.id,
            "node_type": "standard",
            "position_x": 100.0,
            "position_y": 100.0,
            "execution_order": 1
        }
        response = client.post("/api/v1/testcase-chains/nodes", json=node_data, headers=headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["chain_id"] == chain.id
        assert data["testcase_id"] == test_case.id
        assert data["node_type"] == "standard"

    def test_create_chain_edge_api(self, test_db: Session):
        """Test creating a chain edge via API."""
        # Create user and get token
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.ANALYST,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Create campaign and test cases
        campaign = CampaignDB(
            name="Test Campaign",
            description="Test Description",
            created_by=user.id
        )
        test_db.add(campaign)
        test_db.commit()
        
        test_case1 = TestCaseDB(
            name="Test Case 1",
            description="Test Description",
            campaign_id=campaign.id,
            created_by=user.id
        )
        test_case2 = TestCaseDB(
            name="Test Case 2",
            description="Test Description",
            campaign_id=campaign.id,
            created_by=user.id
        )
        test_db.add_all([test_case1, test_case2])
        test_db.commit()
        
        # Create chain and nodes
        chain = TestcaseChainDB(
            name="Test Chain",
            description="Test Description",
            created_by=user.id,
            status="draft"
        )
        test_db.add(chain)
        test_db.commit()
        
        node1 = TestcaseChainNodeDB(
            chain_id=chain.id,
            testcase_id=test_case1.id,
            node_type="start",
            position_x=100.0,
            position_y=100.0,
            execution_order=1
        )
        node2 = TestcaseChainNodeDB(
            chain_id=chain.id,
            testcase_id=test_case2.id,
            node_type="standard",
            position_x=200.0,
            position_y=100.0,
            execution_order=2
        )
        test_db.add_all([node1, node2])
        test_db.commit()
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "SecurePass123!"
        }
        login_response = client.post("/api/token/", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Create edge
        edge_data = {
            "source_node_id": node1.id,
            "target_node_id": node2.id,
            "edge_type": "standard"
        }
        response = client.post("/api/v1/testcase-chains/edges", json=edge_data, headers=headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["source_node_id"] == node1.id
        assert data["target_node_id"] == node2.id
        assert data["edge_type"] == "standard"

    def test_execute_chain_api(self, test_db: Session):
        """Test executing a testcase chain via API."""
        # Create user and get token
        user = User(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.ANALYST,
            full_name="Test User",
            is_active=True,
            email_verified=True
        )
        user.set_password("SecurePass123!")
        test_db.add(user)
        test_db.commit()
        
        # Create chain with nodes
        chain = TestcaseChainDB(
            name="Test Chain",
            description="Test Description",
            created_by=user.id,
            status="active"
        )
        test_db.add(chain)
        test_db.commit()
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "SecurePass123!"
        }
        login_response = client.post("/api/token/", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Execute chain
        execute_data = {
            "chain_id": chain.id
        }
        response = client.post("/api/v1/chain-executions/", json=execute_data, headers=headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["chain_id"] == chain.id
        assert data["started_by"] == user.id
        assert data["status"] in ["pending", "running"]
